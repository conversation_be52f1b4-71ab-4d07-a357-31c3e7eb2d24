{"name": "@uni/components", "version": "1.0.0", "description": "", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "scripts": {"dev": "father dev", "build": "father build", "build:deps": "father prebundle", "prepublishOnly": "father doctor && npm run build"}, "keywords": [], "authors": ["zhang.jun"], "license": "MIT", "files": ["dist", "compiled"], "publishConfig": {"access": "public"}, "dependencies": {"@ant-design/pro-components": "2.3.58", "@ant-design/pro-layout": "^6.5.0", "@ant-design/pro-provider": "2.0.10", "@ant-design/pro-table": "3.13.3", "@ant-design/pro-utils": "2.2.4", "@ant-design/colors": "7.0.0", "@dnd-kit/core": "6.1.0", "@dnd-kit/modifiers": "7.0.0", "@dnd-kit/sortable": "8.0.0", "@dnd-kit/utilities": "3.2.2", "@tanstack/react-virtual": "3.13.2", "@uni/commons": "workspace:*", "@uni/rc-select": "git+http://rc-select:RQrk3h4aDfG-ZRRpzMss@************:8088/masachi.zhang/rc-select.git#v14.1.52", "@uni/utils": "workspace:*", "@uni/hooks": "workspace:*", "antd": "4.24.2", "classnames": "2.3.2", "echarts": "^5.4.0", "invariant": "^2.2.4", "lodash": "^4.17.21", "rc-cascader": "3.10.3", "rc-util": "^5.0.1", "react-color": "^2.19.3", "react-hotkeys-hook": "^4.4.0", "react-imask": "7.1.3", "imask": "7.1.3", "warning": "^4.0.2", "virtualizedtableforantd4": "^1.3.1", "@tanstack/react-table": "8.11.2", "react-responsive-carousel": "3.2.23", "@webcam/react": "1.0.1", "react-image-gallery": "^1.2.9", "react-resizable": "^3.0.5"}, "devDependencies": {"father": "4.1.3"}, "pnpm": {"overrides": {"rc-tabs": "git+http://rc-tabs:qc8x-Xvpjgsyzs1nKeu_@************:8088/masachi.zhang/rc-tabs.git#v12.3.2"}}}