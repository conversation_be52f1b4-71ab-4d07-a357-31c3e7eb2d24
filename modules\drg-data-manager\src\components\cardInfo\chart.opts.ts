import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import _ from 'lodash';

const FeeTypesBarOption = (data, category) => {
  // data = _.orderBy(data, [(d) => d?.TotalFee || ''], 'desc');
  let color = theme.color;
  let option = {
    dataset: {
      source: data,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
      icon: 'circle',
    },
    grid: {
      x: '15%',
      x2: '5%',
      y: '20%',
    },
    xAxis: {
      type: 'category',
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: 9,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 9,
      },
    ],
    yAxis: [
      {
        name: '费用',
        position: 'left',
        confine: true,
        customType: 'currency',
        offset: 10,
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
    ],
    series: [
      {
        name: '当前费用',
        type: 'bar',
        barWidth: 10,
        label: {
          normal: {
            // show: true,
          },
        },
        lineStyle: {
          normal: {
            width: 3,
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[0],
          },
        },
        encode: {
          y: ['TotalFee'],
          x: category,
          tooltip: ['TotalFee'],
        },
      },
      {
        name: '历史费用',
        type: 'bar',
        barWidth: 10,
        lineStyle: {
          normal: {
            width: 3,
            type: 'dashed',
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[2],
          },
        },
        encode: {
          y: ['AvgFee'],
          x: category,
          tooltip: ['AvgFee'],
        },
      },
    ],
  };
  return option;
};

export { FeeTypesBarOption };
