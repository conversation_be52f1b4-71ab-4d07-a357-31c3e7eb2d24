@import '~@uni/commons/src/style/variables.less';

@collapse-border: 1px solid @card-border-color;

.card-info-container {
  .ant-collapse {
    background-color: transparent;
    // border: @collapse-border;
  }

  .ant-collapse > .ant-collapse-item > .ant-collapse-header,
  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }

  .ant-card-head-title {
    .ant-btn-icon-only {
      height: 16px;
    }
  }

  .base-info-descriptions {
    .ant-descriptions-item-content {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.ant-alert-orange {
  background-color: #fff3e4b5;
  border: 1px solid #ffbd76;
}
