import { name } from './package.json';
import { slaveCommonConfig } from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/drgDataManager/',
  outputPath: '../../dist/drgDataManager',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  dva: {
    immer: true,
    hmr: true,
  },

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/hqmsDataManagement',
    },
    {
      path: '/hqmsCardImport',
      exact: true,
      component: '@/pages/hqmsImportFile/index',
    },
    {
      path: '/hqmsDataManagement',
      exact: true,
      component: '@/pages/hqmsDataManagement/index',
    },
    {
      path: '/maintenanceDataCnt',
      exact: true,
      component: '@/pages/MaintenanceDataCnt/index',
    },
    {
      path: '/maintenanceRecordDataCnt',
      exact: true,
      component: '@/pages/MaintenanceRecordDataCnt/index',
    },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/common': {
      target: 'http://172.16.3.152:5181',
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
