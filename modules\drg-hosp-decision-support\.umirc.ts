import { name } from './package.json';
import { slaveCommonConfig } from '../../.umirc.commom';

export default {
  base: name,
  publicPath: '/drgHospDecisionSupport/',
  outputPath: '../../dist/drgHospDecisionSupport',
  mountElementId: 'microAppContent',

  hash: true,

  ...slaveCommonConfig,

  dva: {
    immer: true,
    hmr: true,
  },

  qiankun: { slave: {} },

  plugins: [
    require.resolve('@uni/commons/src/plugins/corejs.ts'),
    require.resolve('@uni/commons/src/plugins/inject-env.ts'),
    require.resolve('@uni/commons/src/plugins/document-title.ts'),
  ],

  routes: [
    {
      path: '/',
      exact: true,
      redirect: '/hospLevel/Cmi',
    },
    // {
    //   path: '/hqmsCardImport',
    //   exact: true,
    //   component: '@/pages/hqmsImportFile/index',
    // },
    // {
    //   path: '/hqmsDataManagement',
    //   exact: true,
    //   component: '@/pages/hqmsDataManagement/index',
    // },
    {
      path: '/hospLevel',
      routes: [
        {
          path: '/hospLevel/Cmi',
          exact: true,
          component: '@/pages/hospLevel/hospCmi/index',
        },
        {
          path: '/hospLevel/diseaseType',
          exact: true,
          component: '@/pages/hospLevel/diseaseType/index',
        },
        {
          path: '/hospLevel/groupType',
          exact: true,
          component: '@/pages/hospLevel/groupType/index',
        },
        // {
        //   path: '/hospLevel/SdComposition',
        //   exact: true,
        //   component: '@/pages/hospLevel/SdComposition/index',
        // },
      ],
    },
    {
      path: '/majoePerfDept',
      routes: [
        {
          path: '/majoePerfDept/Cmi',
          exact: true,
          component: '@/pages/majoePerfDept/cmi/index',
        },
      ],
    },
    {
      path: '/deptLevel',
      routes: [
        {
          path: '/deptLevel/Cmi',
          exact: true,
          component: '@/pages/deptLevel/deptCmi/index',
        },
        {
          path: '/deptLevel/diseaseType',
          exact: true,
          component: '@/pages/deptLevel/deptDiseaseType/index',
        },
        {
          path: '/deptLevel/groupType',
          exact: true,
          component: '@/pages/deptLevel/groupType/index',
        },
        // {
        //   path: '/deptLevel/SdComposition',
        //   exact: true,
        //   component: '@/pages/deptLevel/deptSdComposition/index',
        // },
      ],
    },
    {
      path: '/medTeamLevel',
      routes: [
        {
          path: '/medTeamLevel/Cmi',
          exact: true,
          component: '@/pages/medTeamLevel/medTeamCmi/index',
        },
        {
          path: '/medTeamLevel/diseaseType',
          exact: true,
          component: '@/pages/medTeamLevel/medTeamDiseaseType/index',
        },
        {
          path: '/medTeamLevel/groupType',
          exact: true,
          component: '@/pages/medTeamLevel/groupType/index',
        },
        // {
        //   path: '/deptLevel/operRate',
        //   exact: true,
        //   component: '@/pages/deptLevel/deptOperRate/index',
        // },
        // {
        //   path: '/deptLevel/SdComposition',
        //   exact: true,
        //   component: '@/pages/deptLevel/deptSdComposition/index',
        // },
      ],
    },
    // 院级明细
    {
      path: '/detail',
      routes: [
        {
          path: '/detail/hospDrgs',
          exact: true,
          component: '@/pages/details/hospDrgs/index',
        },
        {
          path: '/detail/hospOper',
          exact: true,
          component: '@/pages/details/hospOper/index',
        },
        {
          path: '/detail/hospSd',
          exact: true,
          component: '@/pages/details/hospSd/index',
        },
      ],
    },
    // 疑难病例
    {
      path: '/difficultCases',
      routes: [
        {
          path: '/difficultCases/hosp',
          exact: true,
          component: '@/pages/hospLevel/difficultCase/index',
        },
        {
          path: '/difficultCases/dept',
          exact: true,
          component: '@/pages/deptLevel/difficultCase/index',
        },
        {
          path: '/difficultCases/medTeam',
          exact: true,
          component: '@/pages/medTeamLevel/difficultCase/index',
        },
      ],
    },
    // 外科能力
    {
      path: '/surgicalAbility',
      routes: [
        {
          path: '/surgicalAbility/hosp',
          exact: true,
          component: '@/pages/hospLevel/operRate/index',
        },
        {
          path: '/surgicalAbility/dept',
          exact: true,
          component: '@/pages/deptLevel/operRate/index',
        },
        {
          path: '/surgicalAbility/medTeam',
          exact: true,
          component: '@/pages/medTeamLevel/operRate/index',
        },
      ],
    },
    // 重点监控
    {
      path: '/sdComposition',
      routes: [
        {
          path: '/sdComposition/hosp',
          exact: true,
          component: '@/pages/hospLevel/SdComposition/index',
        },
        {
          path: '/sdComposition/dept',
          exact: true,
          component: '@/pages/deptLevel/SdComposition/index',
        },
        {
          path: '/sdComposition/medTeam',
          exact: true,
          component: '@/pages/medTeamLevel/SdComposition/index',
        },
      ],
    },
    // 医疗质量
    {
      path: '/medicalQuality',
      routes: [
        {
          path: '/medicalQuality/hosp',
          exact: true,
          component: '@/pages/hospLevel/medicalQuality/index',
        },
        {
          path: '/medicalQuality/dept',
          exact: true,
          component: '@/pages/deptLevel/medicalQuality/index',
        },
        {
          path: '/medicalQuality/medTeam',
          exact: true,
          component: '@/pages/medTeamLevel/medicalQuality/index',
        },
      ],
    },
  ],

  proxy: {
    // 同cra的setupProxy,代理中转实现dev版本的跨域
    '/common': {
      target: 'http://172.16.3.152:5181', // http://172.16.3.217:8001/swagger/index.html
      changeOrigin: true,
      pathRewrite: { '^/common': '' },
      secure: false, // https的dev后端的话需要配
    },
  },
};
