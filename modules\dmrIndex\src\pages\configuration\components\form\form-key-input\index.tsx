import './index.less';
import { Button, Form, Input, Modal, Popconfirm } from 'antd';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
} from '@uni/components/src/pro-form';
import React, { useEffect } from 'react';

const diagnosisKeyToFormKey = {
  // 入院
  IcdeAdmsItem: 'IcdeAdms',
  IcdeAdmsIcdeName: 'IcdeAdms',

  // 入院疾病诊断
  IcdeOtpsIcdeName: 'IcdeOtps',
  IcdeOtpsItem: 'IcdeOtps',

  // 损伤中毒诊断
  IcdeDamgsItem: 'IcdeDamgs',
  IcdeDamgsIcdeName: 'IcdeDamgs',

  // 病理疾病诊断
  IcdePathosItem: 'IcdePathos',
  IcdePathosIcdeName: 'IcdePathos',

  TcmIcdeOtpsIcdeName: 'TcmIcdeOtps',
  TcmIcdeOtpsItem: 'TcmIcdeOtps',

  TcmIcdeOtpsMainIcdeName: 'TcmIcdeOtpsMain',
  TcmIcdeOtpsMainItem: 'TcmIcdeOtpsMain',

  // 颅脑损伤 下面有写了 这个可以没必要用
  Unconscious: '',
};

const FormKeyInput = (props: any) => {
  const form = Form.useFormInstance();

  const keyValue = Form.useWatch('data.key', form);
  const formKeyValue = Form.useWatch('data.form.key', form);

  const formKeyDiagnosisValueProcessor = () => {
    return diagnosisKeyToFormKey[keyValue] ?? formKeyValue;
  };

  return (
    <ProFormText
      {...props}
      fieldProps={{
        ...props?.fieldProps,
        onChange: (event) => {
          form?.setFieldValue(props?.name, event?.target?.value);
        },
        value: formKeyDiagnosisValueProcessor(),
      }}
    />
  );
};

export default FormKeyInput;
