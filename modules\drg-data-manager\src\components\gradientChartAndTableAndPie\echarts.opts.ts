import _ from 'lodash';

const FeeChargeDistributioByMedChrgitmLabelLinePieOption = (
  originData,
  category,
) => {
  let data = [];
  if (originData?.Stats?.length) {
    data = _.orderBy(originData?.Stats, [`TotalFee`], 'desc');
  } else return {};
  const option = {
    dataset: {
      source: data,
    },
    legend: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        type: 'pie',
        encode: {
          itemName: category,
          value: `TotalFee`,
        },
        radius: ['30%', '55%'],
        center: ['50%', '50%'],
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1,
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80,
        },
        label: {
          show: true,
          alignTo: 'edge',
          formatter: '{name|{b}}\n{time|{d}%}',
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            name: {
              fontSize: 12,
            },
            time: {
              fontSize: 12,
              color: '#999',
            },
          },
        },
      },
    ],
  };
  return option;
};

export { FeeChargeDistributioByMedChrgitmLabelLinePieOption };
