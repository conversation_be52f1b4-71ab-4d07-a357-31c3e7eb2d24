export const CodeValueStats = [
  {
    headerTitle: '编码修改',
    contentData: 'PatCnt',
    title: '修改主诊断例数',
    clickable: true,
  },
  {
    contentData: 'PatCnt',
    title: '添加次诊例数',
    clickable: true,
  },
  {
    contentData: 'PatCnt',
    title: '修改主手术例数',
    clickable: true,
  },
  {
    contentData: 'PatCnt',
    title: '增加手术例数',
    clickable: true,
  },
  {
    headerTitle: '绩效DRG',
    contentData: 'Cmi',
    title: 'CMI',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '入组率',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '疑难病例例数',
    clickable: true,
    footer: true,
    footerData: 'DrgCnt',
    footerTitle: '编码前',
  },
  {
    contentData: 'PatCnt',
    title: '低风险死亡人次',
    clickable: true,
    footer: true,
  },
  {
    headerTitle: '国考',
    contentData: 'PatCnt',
    title: '三四级手术例数',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '四级手术例数',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '三级手术例数',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '微创手术例数',
    clickable: true,
    footer: true,
  },
  {
    headerTitle: '等评',
    contentData: 'PatCnt',
    title: '低风险死亡人次',
    clickable: true,
    footer: true,
  },
  {
    headerTitle: '医保支付',
    contentData: 'PatCnt',
    title: '盈亏',
    clickable: true,
    footer: true,
  },
  {
    contentData: 'PatCnt',
    title: '正常入组率',
    clickable: true,
    footer: true,
  },
];

export const ELEMENT_TYPES = {
  CODER: 'Coder',
  CLIDEPT: 'CliDept',
  MEDTEAM: 'MedTeam',
  CHIEF: 'Chief',
  MAJOR_PERF_DEPT: 'MajorPerfDept', // 绩效科室
} as const;

export type ElementType = typeof ELEMENT_TYPES[keyof typeof ELEMENT_TYPES];

export const ELEMENT_CONFIG: Record<
  ElementType,
  {
    key: ElementType;
    title: string;
    tabKey: string;
    tabLabel: string;
    dictModule: string;
    dictModuleGroup: string;
  }
> = {
  [ELEMENT_TYPES.CODER]: {
    key: ELEMENT_TYPES.CODER,
    title: '编码员',
    tabKey: 'coderTableStatistic',
    tabLabel: '编码员工作量分析',
    dictModule: 'Coder',
    dictModuleGroup: 'Dmr',
  },
  [ELEMENT_TYPES.CLIDEPT]: {
    key: ELEMENT_TYPES.CLIDEPT,
    title: '科室',
    tabKey: 'cliDeptTableStatistic',
    tabLabel: '科室工作量分析',
    dictModule: 'CliDepts',
    dictModuleGroup: 'Dmr',
  },
  [ELEMENT_TYPES.MEDTEAM]: {
    key: ELEMENT_TYPES.MEDTEAM,
    title: '医疗组',
    tabKey: 'medTeamTableStatistic',
    tabLabel: '医疗组工作量分析',
    dictModule: 'MedTeams',
    dictModuleGroup: 'Dmr',
  },
  // 绩效科室
  [ELEMENT_TYPES.MAJOR_PERF_DEPT]: {
    key: ELEMENT_TYPES.MAJOR_PERF_DEPT,
    title: '绩效科室',
    tabKey: 'majorPerfDeptTableStatistic',
    tabLabel: '绩效科室工作量分析',
    dictModule: 'MajorPerfDepts', // ???
    dictModuleGroup: 'Dmr',
  },
  [ELEMENT_TYPES.CHIEF]: {
    key: ELEMENT_TYPES.CHIEF,
    title: '责任医生',
    tabKey: 'chiefTableStatistic',
    tabLabel: '责任医生工作量分析',
    dictModule: 'Employee',
    dictModuleGroup: 'Dmr',
  },
};

export const API_ENDPOINTS = {
  GET_METRICS: 'Api/DmrAnalysis/CoderEfficiency/GetMetrics',
  GET_STATS: 'Api/DmrAnalysis/CoderEfficiency/GetStats',
  GET_GROUPER_LIST: 'Api/Analysis/AnaModelDef/GetGrouperList',
  GET_DETAILS: 'Api/DmrAnalysis/CoderEfficiency/GetDetails',
  GET_OPER_DRILLDOWN_METRICS:
    'Api/DmrAnalysis/CoderEfficiency/GetOperDrillDownMetrics',
  GET_OPER_DRILLDOWN_STATS:
    'Api/DmrAnalysis/CoderEfficiency/GetOperDrillDownStats',
  Get_Stats_By_Month: 'Api/DmrAnalysis/CoderEfficiency/GetStatsByMonth',
} as const;

export const DEFAULT_TAB_KEY = 'statistic';
export const DEFAULT_TAB_LABEL = '指标分项分析';

export const OPERATION_TAB = {
  key: 'operTableStatistic',
  label: '手术工作量分析',
} as const;

export const MONTH_TREND_TAB = {
  key: 'monthTrendStatistic',
  label: '月度工作量分析',
} as const;

export const CodeValueQuantificationEventConstants = {
  STAT_NUMBER_CLK: 'STAT_NUMBER_CLK',
  STAT_ROW_CLK: 'STAT_ROW_CLK',
  SURGICAL_CNT_CLK: 'SURGICAL_CNT_CLK',
  SURGICAL_MODAL_OPEN: 'SURGICAL_MODAL_OPEN',
  STAT_DRILLDOWN_CLICK: 'STAT_DRILLDOWN_CLICK',
  STAT_NESTED_DRILLDOWN_CLICK: 'STAT_NESTED_DRILLDOWN_CLICK',
} as const;
