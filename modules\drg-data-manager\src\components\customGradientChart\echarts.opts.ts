import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import _ from 'lodash';

const BubbleGradientOption = (data, axisOpt, category) => {
  if (!data || !axisOpt || !category) return {};
  // console.log(data, axisOpt, category);
  let xAvg =
    data.reduce((sum, item) => sum + item?.[axisOpt.xAxis.value], 0) /
    data.length;
  let yAvg =
    data.reduce((sum, item) => sum + item?.[axisOpt.yAxis.value], 0) /
    data.length;
  let color = theme.color;
  let option = {
    tooltip: {},
    dataset: {
      source: data,
    },
    yAxis: {
      name: axisOpt.yAxis.label,
      type: 'value',
      nameRotate: 90,
      nameGap: 55,
      nameLocation: 'center',
      min: yAvg
        ? (value) => {
            let distanceToMin =
              Math.abs(value.min - +yAvg) > Math.abs(value.max - +yAvg);
            return (distanceToMin ? value.min : +yAvg - value.max).toFixed(0);
          }
        : 'dataMin',
      max: yAvg
        ? (value) => {
            let distanceToMin =
              Math.abs(value.min - +yAvg) > Math.abs(value.max - +yAvg);
            return (distanceToMin ? yAvg - value.min : value.max).toFixed(0);
          }
        : 'dataMax',
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
        show: true,
      },
    },
    xAxis: {
      name: axisOpt.xAxis.label,
      type: 'value',
      nameLocation: 'center',
      nameGap: 35,
      min: xAvg
        ? (value) => {
            let distanceToMin =
              Math.abs(value.min - +xAvg) > Math.abs(value.max - +xAvg);

            return (distanceToMin ? value.min : +xAvg - value.max).toFixed(0);
          }
        : 'dataMin',
      max: xAvg
        ? (value) => {
            let distanceToMin =
              Math.abs(value.min - +xAvg) > Math.abs(value.max - +xAvg);
            return (distanceToMin ? xAvg - value.min : value.max).toFixed(0);
          }
        : 'dataMax',
      axisLine: {
        show: false,
        onZero: false,
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
        show: true,
      },
    },
    grid: {
      left: '15%',
      right: '12%',
      top: '12%',
      bottom: '17%',
    },
    toolbox: {
      zlevel: 2,
      show: true,
      right: '10%',
      top: '0%',
      feature: {
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
      },
    },
    series: [
      {
        type: 'scatter',
        zlevel: 1,
        symbolSize: 12,
        // symbolSize: (data) => {
        //   return Math.sqrt(data?.['PatCnt']) * 3 || 12;
        // },
        // function (data) {
        //   return  / 5e2 // ;
        // },
        encode: {
          y: [axisOpt.yAxis.value],
          x: [axisOpt.xAxis.value],
        },
        tooltip: {
          formatter: (params) => {
            let { value } = params;
            return `${value?.[category]} <br /> 人次：${value.PatCnt}<br /> ${
              axisOpt.yAxis.label
            }：${value[axisOpt.yAxis.value]?.toFixed(2)}<br /> ${
              axisOpt.xAxis.label
            }：${value[axisOpt.xAxis.value]?.toFixed(2)}`;
          },
        },
        itemStyle: {
          opacity: 0.6,
          borderColor: color[6],
          shadowBlur: 10,
          shadowColor: 'rgba(62,57,107,0)',
          shadowOffsetY: 5,
          color: {
            x: 0,
            y: 0,
            x2: 1,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: color[6], // 100% 处的颜色
              },
              {
                offset: 0,
                color: color[2], // 135200 0% 处的颜色
              },
            ],
            globalCoord: false, // 缺省为 false
          },
        },
        markLine: {
          symbol: ['none', 'none'],
          silent: true,
          lineStyle: {
            normal: {
              color: '#6a7985',
              type: 'dashed',
            },
          },
          label: {
            show: true,
            position: 'end',
          },
          data: [
            {
              xAxis: valueNullOrUndefinedReturnDash(xAvg),
            },
            {
              yAxis: valueNullOrUndefinedReturnDash(yAvg),
            },
          ],
        },
      },
    ],
  };
  return option;
};

export { BubbleGradientOption };
