import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ProDescriptions } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Tag,
  Card,
  Badge,
  Row,
  Col,
  Tooltip,
  Tabs,
  Modal,
  Divider,
  Alert,
  Space,
} from 'antd';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import UniEcharts from '@uni/components/src/echarts';
import { UniTable } from '@uni/components/src';
import './index.less';
import { FeeTypesBarOption } from '../../chart.opts';
import FeeTypesContent from '../feeTypesContent';
import FeeChargesContent from '../feeChargesContent';

const FeeTypes = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  return (
    <>
      <Tabs
        size="small"
        items={[
          {
            key: 'statsItems',
            label: '统计类别',
            children: (
              <>
                <Row gutter={[0, 16]}>
                  <Col span={24}>
                    <FeeTypesContent
                      data={props?.data}
                      type={'Stats'}
                      columns={props?.columns}
                      loading={props?.loading}
                    />
                  </Col>
                  <Col span={24}>
                    <FeeChargesContent
                      chsDrgCode={props?.chsDrgCode}
                      hisId={props?.hisId}
                      tabData={props?.data}
                      chargeTypeMode={'Stats'}
                    />
                  </Col>
                </Row>
              </>
            ),
          },
          {
            key: 'tarItems',
            label: '收费项目类别',
            children: (
              <>
                <Row gutter={[0, 16]}>
                  <Col span={24}>
                    <FeeTypesContent
                      data={props?.data}
                      type={'Med'}
                      columns={props?.columns}
                      loading={props?.loading}
                    />
                  </Col>
                  <Col span={24}>
                    <FeeChargesContent
                      chsDrgCode={props?.chsDrgCode}
                      hisId={props?.hisId}
                      tabData={props?.data}
                      chargeTypeMode={'Med'}
                    />
                  </Col>
                </Row>
              </>
            ),
          },
        ]}
      />
    </>
  );
};
export default FeeTypes;
