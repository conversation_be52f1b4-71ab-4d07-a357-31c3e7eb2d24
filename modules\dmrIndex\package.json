{"name": "dmr", "private": true, "scripts": {"start": "umi dev", "build": "umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"react": "18.x", "react-dom": "18.x", "umi": "3.5.34", "@uni/components": "workspace:^", "@uni/utils": "workspace:^", "@uni/services": "workspace:^", "@uni/commons": "workspace:^", "@uni/grid": "workspace:^", "react-hotkeys-hook": "^4.4.0", "react-resize-detector": "9.0.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "resize-observer-polyfill": "^1.5.1", "rc-overflow": "1.3.2", "@uiw/react-color": "2.0.6", "mathjs": "11.12.0", "typed-function": "4.1.1", "@react-awesome-query-builder/antd": "6.1.3", "@react-awesome-query-builder/ui": "6.1.3", "@react-awesome-query-builder/core": "6.1.3", "chevrotain": "10.5.0", "fuzzysort": "2.0.4", "immutable": "^3.8.2", "complex.js": "2.1.1", "diff": "7.0.0"}, "devDependencies": {"babel-loader": "^8.2.5", "babel-plugin-import": "^1.13.5", "typescript": "^4.1.2", "@types/react": "18.x", "@types/react-dom": "18.x", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/preset-react": "1.x", "@umijs/test": "3.5.34", "yorkie": "^2.0.0"}, "pnpm": {"overrides": {"rc-tabs": "git+*****************************************************/masachi.zhang/rc-tabs.git#v12.3.2"}}}