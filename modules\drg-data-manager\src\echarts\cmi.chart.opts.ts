import theme from '@uni/components/src/echarts/themes/themeBlueYellow';

import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

import _ from 'lodash';

const CmiBmRadar = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  let option = {
    legend: {
      show: true,
      right: 10,
      data: [
        {
          name: '全部',
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[0], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[2], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        {
          name: category,
          itemStyle: {
            color: color[1],
          },
        },
      ],
    },
    tooltip: {
      // trigger: 'axis',
    },
    dataset: {
      source: data,
    },
    radar: {
      shape: 'polygon',
      indicator: [
        { name: '低风险死亡率', max: 1 },
        { name: 'CMI' },
        { name: '组数' },
        { name: '平均住院天数' },
        { name: '材料费占比', max: 1 },
        { name: '药占比', max: 1 },
      ],
    },
    series: [
      {
        type: 'radar',
        label: {
          normal: {
            show: false,
          },
        },
        tooltip: {
          //TODO
          // formatter: (params, ticket, callback) => {
          //   console.log(params)
          //   return 'aaa'
          // }
        },
        data: [
          {
            name: '全部',
            value: Object.keys(data[0]).map((d) => data[0][d]),
            lineStyle: {
              width: 0,
              color: color[0],
            },
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 1,
                    color: color[0], // 100% 处的颜色
                  },
                  {
                    offset: 0,
                    color: color[1], // 0% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
          },
          {
            name: category,
            value: Object.keys(data[1]).map((d) => data[1][d]),
            lineStyle: {
              width: 2,
              color: color[1],
            },
          },
        ],
      },
    ],
  };

  return option;
};

const CmiPie = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  // if (data) {
  //   data = _.orderBy(data, [`${category}Cnt`], 'desc');
  // } else return {};
  const option = {
    baseOption: {
      dataset: {
        source: data,
      },
      legend: {},
      tooltip: {
        trigger: 'item',
      },
      series: [
        {
          type: 'pie',
          encode: {
            itemName: ['RwRangeName'],
            value: ['PatRatio'],
            tooltip: ['PatCnt'],
          },
          selectedMode: 'single',
          selectedOffset: 6,
          label: {
            show: true,
            fontSize: '13',
            formatter: '{d}%',
          },
          labelLine: {
            show: true,
          },
          select: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          emphasis: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
          },
        },
      ],
    },
    media: [
      {
        option: {
          legend: {
            show: true,
            top: 'middle',
            right: '5%',
            orient: 'vertical',
          },
          series: [
            {
              radius: ['50%', '75%'],
              center: ['40%', '50%'],
              // emphasis: {
              //   label: {
              //     show: true,
              //   },
              // },
            },
          ],
        },
      },
      {
        //小屏
        query: {
          maxWidth: 420,
          minWidth: 0,
        },
        option: {
          legend: {
            show: true,
            top: 'top',
            right: 'auto',
            orient: 'horizontal',
          },
          series: [
            {
              radius: ['0%', '50%'],
              center: ['50%', '60%'],
            },
          ],
        },
      },
    ],
  };
  return option;
};

const CmiLineBar = (data, category) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  let option = {
    dataset: {
      source: data,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
    },
    grid: {},
    xAxis: {
      type: 'category',
      scale: true,
      boundaryGap: true,
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: 4,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 4,
      },
    ],
    yAxis: [
      {
        name: '出院人次',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
      {
        name: '人次占比',
        position: 'right',
      },
    ],
    series: [
      {
        name: '出院人次',
        type: 'bar',
        barWidth: 10,
        label: {
          normal: {
            show: true,
            // formatter: ({ value }) => {
            //   return checkType === 'MiddleOrAfter'
            //     ? valueFormatter(value['违规收费金额'], 'Currency')
            //     : value['医嘱违规次数'];
            // },
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0.7,
                  color: color[2], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[1], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        encode: {
          y: ['PatCnt'],
          x: category,
          tooltip: ['PatCnt'],
        },
      },
      {
        name: '人次占比',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: (value) => {
            return valueNullOrUndefinedReturnDash(value, 'Percent');
          },
        },
        itemStyle: {
          normal: {
            color: color[1],
          },
        },
        lineStyle: {
          normal: {
            color: color[1],
            width: 2,
            type: 'dotted',
          },
        },
        encode: {
          x: category,
          y: ['PatRatio'],
          value: ['PatRatio'],
        },
      },
    ],
  };
  return option;
};

const CmiTrendsLine = (data, category) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  let length = 4;
  let option = {
    dataset: {
      source: data,
    },
    legend: {
      show: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: length,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: length,
      },
    ],
    xAxis: {
      type: 'category',
      // axisLabel: {
      //   formatter: (value, index) => {
      //     return valueNullOrUndefinedReturnDash(value, 'Month');
      //   },
      // },
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
    ],
    series: [
      {
        name: 'CMI',
        type: 'line',
        label: {
          normal: {
            show: true,
          },
        },
        // symbol: 'none',
        lineStyle: {
          width: 5,
          color: {
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 1,
                color: color[3], // 100% 处的颜色
              },
              {
                offset: 0,
                color: color[0], // 0% 处的颜色
              },
            ],
            globalCoord: false, // 缺省为 false
          },
          shadowColor: 'rgba(62,57,107,.14)',
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        encode: {
          x: category,
          y: ['Cmi'],
          tooltip: ['Cmi'],
        },
      },
    ],
  };
  return option;
};

const ADrgLineBar = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let length = 9;
  let color = theme.color;
  let option = {
    dataset: {
      source: _.orderBy(data, [`PatCnt`], 'desc'),
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
    },
    grid: {},
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        minValueSpan: length,
        startValue: 0, //数值index
        endValue: length,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: length,
      },
    ],
    xAxis: {
      type: 'category',
      axisLabel: {
        formatter: getLinebreakFormat(6, 2, 7), //文字保留
      },
    },
    yAxis: [
      {
        name: '出院人次',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
      {
        name: '平均RW',
        position: 'right',
        // max: function(value) {
        //   return value.max + value.min;
        // },
      },
    ],
    series: [
      {
        name: '出院人次',
        type: 'bar',
        barWidth: 10,
        label: {
          normal: {
            show: true,
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[0], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[0], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        encode: {
          y: 'PatCnt',
          x: category,
          tooltip: 'PatCnt',
        },
      },
      {
        name: '平均RW',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          normal: {
            color: color[1],
          },
        },
        lineStyle: {
          normal: {
            color: color[1],
            width: 1.5,
            type: 'dotted',
          },
        },
        encode: {
          y: 'AvgRw',
          x: category,
          tooltip: 'AvgRw',
        },
      },
    ],
  };
  return option;
};

export { CmiBmRadar, CmiLineBar, CmiTrendsLine, CmiPie, ADrgLineBar };
