import theme from '@uni/components/src/echarts/themes/themeBlueYellow';

import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

import _ from 'lodash';

const BmBarCharts = (
  columns,
  data, // bm data
  ownData, // own data
  selectedKey, // 哪个指标
  BmChartSelectOptions, // 指标
) => {
  if (_.isEmpty(data) || !ownData || !selectedKey) {
    return {};
  }
  let color = theme.color;
  let option = {
    dataset: {
      source: _.orderBy([...data?.slice(), ...ownData], selectedKey, 'desc'),
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        // params?.at(0)?.data
        console.log(params?.at(0), columns);
        let resultStr = '医院：' + params?.at(0)?.name + '<br />';
        BmChartSelectOptions.forEach((key) => {
          let col = columns?.find((col) => col?.dataIndex === key);
          resultStr +=
            col?.title +
            ': ' +
            valueNullOrUndefinedReturnDash(
              params?.at(0)?.data?.[key],
              col?.dataType,
            ) +
            '<br />';
        });
        return resultStr;
      },
    },
    legend: {
      show: true,
    },
    grid: {},
    xAxis: {
      type: 'category',
      scale: true,
      boundaryGap: true,
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: 9,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 9,
      },
    ],
    yAxis: {
      name: '',
      position: 'left',
      splitLine: {
        show: true,
      },
      axisLabel: {
        formatter: function (value) {
          return valueNullOrUndefinedReturnDash(
            value,
            columns?.find((col) => col.dataIndex === selectedKey)?.dataType,
          );
        },
      },
    },
    series: [
      {
        name: '标杆值',
        type: 'bar',
        barWidth: 10,
        label: {
          normal: {
            show: false,
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0.7,
                  color: color[4], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[0], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        encode: {
          y: [selectedKey],
          x: 'HospName',
          tooltip: [selectedKey],
        },
      },
    ],
  };

  return option;
};

const BmRadarCharts = (
  columns,
  data,
  ownData,
  BmChartSelectOptions, // 指标
) => {
  if (_.isEmpty(data) || _.isEmpty(columns) || _.isEmpty(ownData)) {
    return {};
  }
  console.log(
    'BMRADAR',
    data,
    columns,
    BmChartSelectOptions,
    BmChartSelectOptions.map((key) => data?.at(0)?.[`${key}BmSelf`]),
    BmChartSelectOptions.map((key) => data?.at(0)?.[`${key}BmArea`]),
    BmChartSelectOptions.map((d) => ({
      name: columns?.find((col) => col?.data === d)?.title,
    })),
  );
  let color = theme.color;
  let option = {
    legend: {
      show: true,
      right: 10,
      data: [
        {
          name: '全部',
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[0], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[7], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        {
          name: '区域',
          itemStyle: {
            color: color[0],
          },
        },
        // {
        //   name: '重点DRG组覆盖率',
        //   itemStyle: {
        //     color: color[2],
        //   },
        // },
      ],
    },
    tooltip: {
      order: 'seriesDesc',
    },
    radar: {
      shape: 'polygon',
      indicator: BmChartSelectOptions.map((d) => ({
        name: columns?.find((col) => col?.data === d)?.title,
      })),
    },
    series: [
      {
        type: 'radar',
        label: {
          normal: {
            show: false,
          },
        },
        tooltip: {
          formatter: (params) => {
            let resultStr = params?.name + '<br />';
            console.log('tooltip', params);
            BmChartSelectOptions.forEach((key, index) => {
              let col = columns?.find((col) => col?.data === key);
              resultStr +=
                params.marker +
                col?.title +
                ': ' +
                valueNullOrUndefinedReturnDash(
                  params?.value?.[index],
                  col?.dataType,
                ) +
                '<br />';
            });
            return resultStr;
          },

          //   valueFormatter: (value) => {
          //     console.log('valueFormatter', BmChartSelectOptions)
          //     return valueNullOrUndefinedReturnDash(
          //       value,
          //       columns?.find(
          //         (col) => col?.data === BmChartSelectOptions[dataIndex],
          //       )?.dataType,
          //     );
          //   },
        },
        data: [
          {
            name: '全部',
            value: BmChartSelectOptions.map(
              (key) => ownData?.at(0)?.[`${key}`] || 0,
            ),
            lineStyle: {
              width: 0,
            },
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 1,
                    color: color[0], // 100% 处的颜色
                  },
                  {
                    offset: 0,
                    color: color[7], // 0% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
          },
          {
            name: '区域',
            value: BmChartSelectOptions.map(
              (key) => data?.at(0)?.[`${key}BmArea`] || 0,
            ),
            lineStyle: {
              width: 2,
              color: color[0],
            },
          },
          // {
          //   name: '重点DRG组覆盖率',
          //   value: data.map((d) => (d.KeyDrgCoverage * 100).toFixed(2)),
          //   lineStyle: {
          //     width: 2,
          //     color: color[2],
          //   },
          // },
        ],
      },
    ],
  };
  console.log('BMRADAR', option);
  return option;
};

export { BmBarCharts, BmRadarCharts };
