import React, { useEffect, useRef, useState } from 'react';
import {
  Card,
  Space,
  Input,
  Tooltip,
  Tag,
  Select,
  TableProps,
  message,
} from 'antd';
import { debounce } from 'lodash';
import { useRequest } from 'umi';
import TreeCtrlTable from '@uni/components/src/tree-ctrl-table';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { v4 as uuidv4 } from 'uuid';
import { SorterResult } from 'antd/lib/table/interface';
import { useUpdateEffect } from 'ahooks';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import _ from 'lodash';

// 简单实现dataTableFilterSorterHandler以避免导入问题
const dataTableFilterSorterHandler = (
  columns: any[],
  filters: any,
  sorter: SorterResult<any>,
) => {
  let orderField = '';
  let orderDir = '';

  if (sorter && sorter.field && sorter.order) {
    orderField = Array.isArray(sorter.field)
      ? sorter.field.join('.')
      : sorter.field.toString();
    orderDir = sorter.order === 'ascend' ? 'asc' : 'desc';
  }

  const filterFields = {};
  if (filters) {
    Object.keys(filters).forEach((key) => {
      if (filters[key] && filters[key].length > 0) {
        filterFields[key] = filters[key];
      }
    });
  }

  return {
    order: [{ column: orderField, dir: orderDir }],
    search: { value: '', regex: false },
    filterFields,
  };
};

interface TreeNodeType {
  key: string;
  title: string;
  name: string;
  value: number;
  children?: TreeNodeType[];
}

interface TreeDataType {
  [propName: string]: number | TreeNodeType[];
  treeDataResult: TreeNodeType[];
}

interface TreeKeySelectedType {
  key: string;
  title?: string;
  node?: any;
}

interface QualityControlTabProps {
  searchParams?: any;
  dictData?: any;
}

const QualityControlTab: React.FC<QualityControlTabProps> = ({
  searchParams,
  dictData,
}) => {
  // 关键字搜索
  const [keyword, setKeyword] = useState('');
  // 树形数据
  const [treeData, setTreeData] = useState<TreeDataType | undefined>(undefined);
  // 选中的树节点
  const [treeKeySelected, setTreeKeySelected] = useState<
    TreeKeySelectedType | undefined
  >({
    key: 'key-all',
    title: '全部问题',
  });
  // 表格数据
  const [hqmsImportCardData, setHqmsImportCardData] = useState<any[]>([]);

  // 分页信息
  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // 防抖搜索
  const debouncedSearch = useRef(
    debounce((value: string) => {
      setKeyword(value);
    }, 300),
  ).current;

  // 获取树形数据
  const { loading: getHqmsImportQcStatsLoading, run: getHqmsImportQcStatsReq } =
    useRequest(
      (data = {}) =>
        uniCommonService('Api/Hqms/MedQualityReport/GetHqmsImportQcStats', {
          method: 'POST',
          data: {
            ..._.omit(data, 'keyword'),
          },
        }),
      {
        manual: true,
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200 && res.data) {
            // 处理树形数据结构
            const data = res.data[0]; // 假设返回是数组形式
            console.log('树形数据:', data);
            if (data?.RuleTypeStats?.length) {
              // 转换数据为树形结构
              const treeNodes = data.RuleTypeStats.map((ruleType: any) => ({
                key: ruleType.RuleType,
                title: ruleType.RuleType,
                name: ruleType.RuleType,
                value: ruleType.RuleTypeCnt,
                children: ruleType.SubTypeStats.map((subType: any) => ({
                  key: `${ruleType.RuleType}|${subType.SubType}`,
                  title: subType.SubType,
                  name: subType.SubType,
                  value: subType.SubTypeCnt,
                  children: subType.RuleStats.map((rule: any) => ({
                    key: `${ruleType.RuleType}|${subType.SubType}|${rule.RuleCode}`,
                    title: rule.DisplayErrMsg,
                    name: rule.DisplayErrMsg,
                    value: rule.RuleCnt,
                  })),
                })).sort((a: any, b: any) => b.value - a.value), // 子类型按数量排序
              })).sort((a: any, b: any) => b.value - a.value); // 类型按数量排序

              setTreeData({
                totalCount: data.ProblematicCardCnt,
                treeDataResult: [
                  {
                    key: 'key-all',
                    title: '全部问题',
                    name: '全部问题',
                    value: data.ProblematicCardCnt,
                    children: treeNodes,
                  },
                ],
              });
            } else {
              setTreeData({
                totalCount: 0,
                treeDataResult: [
                  {
                    key: 'key-all',
                    title: '全部问题',
                    name: '全部问题',
                    value: 0,
                    children: [],
                  },
                ],
              });
            }
            return res.data;
          }
          return undefined;
        },
      },
    );

  // 获取表格列定义
  const {
    data: hqmsImportCardColumnsData,
    mutate: mutateColumns,
    loading: getHqmsImportCardColumnsLoading,
    run: getHqmsImportCardColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(
        'Api/Hqms/MedQualityReport/GetHqmsImportCardByReviewResults',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res?.data?.Columns);
        }
      },
    },
  );

  // 获取表格数据
  const { loading: getHqmsImportCardLoading, run: getHqmsImportCardReq } =
    useRequest(
      (data, current, pageSize, other = {}) => {
        return uniCommonService(
          'Api/Hqms/MedQualityReport/GetHqmsImportCardByReviewResults',
          {
            method: 'POST',
            data: {
              ...data,
              DtParam: {
                Draw: 1,
                Start: (current - 1) * pageSize,
                Length: pageSize,
                ...other,
              },
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (res: RespVO<any>) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            setHqmsImportCardData(
              res.data?.data?.map((d: any) => ({ ...d, uuid: uuidv4() })),
            );
            setBackPagination({
              ...backPagination,
              total: res?.data?.recordsTotal || 0,
            });
          } else {
            setHqmsImportCardData([]);
          }
        },
      },
    );

  // 构建请求参数
  const getRequestParams = (page = 1, pageSize = backPagination.pageSize) => {
    const ruleCode =
      treeKeySelected?.key?.includes('|') &&
      treeKeySelected.key.split('|').length > 2
        ? treeKeySelected.key.split('|')[2]
        : undefined;
    return {
      params: {
        keyword,
        SearchKeyWord: keyword,
        ruleTypes:
          !treeKeySelected?.key || treeKeySelected?.key === 'key-all'
            ? undefined
            : [
                treeKeySelected?.key?.includes('|')
                  ? treeKeySelected.key.split('|')[0]
                  : (treeKeySelected?.key as string),
              ],
        subTypes: treeKeySelected?.key?.includes('|')
          ? [treeKeySelected.key.split('|')[1]]
          : undefined,
        // ruleCode:
        //   treeKeySelected?.key?.includes('|') &&
        //   treeKeySelected.key.split('|').length > 2
        //     ? treeKeySelected.key.split('|')[2]
        //     : undefined,
        ruleCodes: ruleCode ? [ruleCode] : undefined,
        sdate: searchParams?.dateRange?.[0],
        edate: searchParams?.dateRange?.[1],
        hospCode: searchParams?.hospCodes,
        errorLevels: searchParams?.errorLevels,
      },
      page,
      pageSize,
    };
  };

  // 表格变化处理
  const backTableOnChange: TableProps<any>['onChange'] = (
    pagi,
    filters,
    sorter,
  ) => {
    setBackPagination({
      ...backPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });

    const { params, page, pageSize } = getRequestParams(
      pagi.current,
      pagi.pageSize,
    );
    getHqmsImportCardReq(
      params,
      page,
      pageSize,
      dataTableFilterSorterHandler([], filters, sorter as any),
    );
  };

  // 树节点选择处理
  const handleTreeSelect = (keys: React.Key[], info: any) => {
    const { node } = info;
    setTreeKeySelected({
      key: node.key,
      title: node.title,
    });
  };

  useUpdateEffect(() => {
    getHqmsImportQcStatsReq({
      sdate: searchParams?.dateRange?.[0],
      edate: searchParams?.dateRange?.[1],
      hospCode: searchParams?.hospCodes,
      errorLevels: searchParams?.errorLevels,
    });
  }, [searchParams]);

  // 搜索条件变化时刷新数据
  useUpdateEffect(() => {
    setBackPagination((prev) => ({ ...prev, current: 1 }));
    const { params, page, pageSize } = getRequestParams(1);
    getHqmsImportCardReq(params, page, pageSize);
  }, [keyword, treeKeySelected, searchParams]);

  // 初始化
  useEffect(() => {
    getHqmsImportCardColumnsReq();

    // 初始化请求数据
    const { params, page, pageSize } = getRequestParams(1, 10);
    getHqmsImportQcStatsReq(params);
    getHqmsImportCardReq(params, page, pageSize);
  }, []);

  return (
    <TreeCtrlTable
      className={'hqms_qc_tree_ctrl_table'}
      treeData={{
        title: '问题分类',
        subTitle: '问题总数',
        subKey: 'totalCount',
        data: treeData,
      }}
      treeLoading={getHqmsImportQcStatsLoading}
      treeAction={{
        onSelect: handleTreeSelect,
      }}
      tableData={{
        tableTitle: (
          <Space>
            <Tooltip title={treeKeySelected?.title}>
              <div
                style={{
                  fontSize: '16px',
                  maxWidth: '300px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {treeKeySelected?.title}
              </div>
            </Tooltip>
            <Input.Search
              placeholder="搜索关键字"
              style={{ width: 200 }}
              allowClear
              onChange={(e) => debouncedSearch(e.target.value)}
            />
          </Space>
        ),
        columnEdit: {
          columnInterfaceUrl:
            'Api/Hqms/MedQualityReport/GetHqmsImportCardByReviewResults',
          onTableRowSaveSuccess: (newColumns) => {
            mutateColumns(tableColumnBaseProcessor([], newColumns));
          },
        },
        columns: hqmsImportCardColumnsData,
        dataSource: hqmsImportCardData,
        rowKey: 'uuid',
        id: 'hqms-import-card-table',
        loading: getHqmsImportCardLoading,
        pagination: backPagination,
        onChange: backTableOnChange,
        dictionaryData: dictData,
        scroll: { x: 'max-content' },
      }}
    />
  );
};

export default QualityControlTab;
