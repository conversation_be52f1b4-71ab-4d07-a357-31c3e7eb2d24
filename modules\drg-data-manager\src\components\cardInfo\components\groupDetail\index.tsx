import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ProDescriptions } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  Tag,
  Card,
  Badge,
  Row,
  Col,
  Tooltip,
  Modal,
  Divider,
  Alert,
  Space,
} from 'antd';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import Progress from '../progress/index';
import { ExclamationCircleTwoTone } from '@ant-design/icons';
import { ChsDrgResultColumns, ChsDipResultColumns } from '../../columns';
import { handleChsLabel } from '@uni/utils/src/cwUtils';
import './index.less';

const GroupDetail = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');

  // 分组条件
  const [isModalOpen, setIsModalOpen] = useState({
    visible: false,
    record: { code: null, name: null },
  });

  const handleChsArr = (array) => {
    let cwTypes = {};
    try {
      cwTypes = JSON.parse(
        sessionStorage?.getItem('configurationInfo') ?? '{}',
      )?.chsDefs;
    } catch (e) {
      console.warn('handleChsArr JSON error', e);
    }
    return handleChsLabel(array, 'key', 'title', cwTypes);
  };

  return (
    <>
      <Card
        size="small"
        title={
          <div>
            <Tooltip
              title={`${props?.metricSettlesData?.ChsDrgCode}${props?.metricSettlesData?.ChsDrgName}`}
            >
              <b className="mr-1">
                {props?.metricSettlesData?.ChsDrgCode}&nbsp;&nbsp;
                {props?.metricSettlesData?.ChsDrgName}
              </b>
            </Tooltip>
            <Tag
              color={
                props?.metricSettlesData?.AbnFeeType === '3'
                  ? 'red'
                  : props?.metricSettlesData?.AbnFeeType === '4'
                  ? 'gold'
                  : props?.metricSettlesData?.AbnFeeType === '0'
                  ? 'orange'
                  : 'green'
              }
            >
              {
                dictData?.Dmr?.AbnFeeType?.find(
                  (a) => a?.Code === props?.metricSettlesData?.AbnFeeType,
                )?.Name
              }
            </Tag>
          </div>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <ProDescriptions
              size="small"
              column={{ xs: 1, sm: 1, md: 2, lg: 4, xl: 4, xxl: 6 }}
              dataSource={props?.metricSettlesData}
              columns={
                // TODO 这边的 type 判断好像不需要了？
                props?.type === 'dip'
                  ? handleChsArr(ChsDipResultColumns(dictData?.Dmr || {}))
                  : handleChsArr(ChsDrgResultColumns(dictData?.Dmr || {}))
              }
            />
          </Col>
          <Col span={24}>
            <Progress data={props?.metricSettlesData || {}} />
          </Col>
        </Row>
      </Card>
    </>
  );
};
export default GroupDetail;
