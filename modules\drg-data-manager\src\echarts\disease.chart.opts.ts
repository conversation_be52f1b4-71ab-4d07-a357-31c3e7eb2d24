import theme from '@uni/components/src/echarts/themes/themeBlueYellow';

import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

import _ from 'lodash';

const MDCRadar = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let color = theme.color;
  let option = {
    legend: {
      show: true,
      right: 10,
      data: [
        {
          name: 'DRG组覆盖率',
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[0], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[7], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        {
          name: '病例数>=5DRG组覆盖率',
          itemStyle: {
            color: color[0],
          },
        },
        // {
        //   name: '重点DRG组覆盖率',
        //   itemStyle: {
        //     color: color[2],
        //   },
        // },
      ],
    },
    tooltip: {
      order: 'seriesDesc',
    },
    dataset: {
      source: data,
    },
    radar: {
      shape: 'polygon',
      indicator: data.map((d) => ({ name: d.MdcNameAbbrev, max: 1 })),
    },
    series: [
      {
        type: 'radar',
        label: {
          normal: {
            show: false,
          },
        },
        tooltip: {
          valueFormatter: (value) => {
            return valueNullOrUndefinedReturnDash(value, 'Percent');
          },
        },
        data: [
          {
            name: 'DRG组覆盖率',
            value: data.map((d) => d.DrgCoverage),
            lineStyle: {
              width: 0,
            },
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 1,
                    color: color[0], // 100% 处的颜色
                  },
                  {
                    offset: 0,
                    color: color[7], // 0% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
          },
          {
            name: '病例数>=5DRG组覆盖率',
            value: data.map((d) => d.MinimumDrgCoverage),
            lineStyle: {
              width: 2,
              color: color[0],
            },
          },
          // {
          //   name: '重点DRG组覆盖率',
          //   value: data.map((d) => (d.KeyDrgCoverage * 100).toFixed(2)),
          //   lineStyle: {
          //     width: 2,
          //     color: color[2],
          //   },
          // },
        ],
      },
    ],
  };
  return option;
};

const CmiBmRadar = (data, category = null) => {
  let color = theme.color;
  let option = {
    legend: {
      show: true,
      right: 10,
      data: [
        {
          name: '全部',
          itemStyle: {
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[0], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[7], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        {
          name: category,
          itemStyle: {
            color: color[1],
          },
        },
      ],
    },
    tooltip: {},
    dataset: {
      source: data,
    },
    radar: {
      shape: 'polygon',
      indicator: [
        { name: '平均住院天数' },
        { name: '均次费用' },
        { name: '材料费占比', max: 1 },
        { name: '药占比', max: 1 },
        { name: '平均RW' },
      ],
    },
    series: [
      {
        type: 'radar',
        label: {
          normal: {
            show: false,
          },
        },
        data: [
          {
            name: '全部',
            value: Object.keys(data[0]).map((d) => data[0][d]),
            lineStyle: {
              width: 0,
              color: color[0],
            },
            areaStyle: {
              color: {
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 1,
                    color: color[0], // 100% 处的颜色
                  },
                  {
                    offset: 0,
                    color: color[7], // 0% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
            },
          },
          {
            name: category,
            value: Object.keys(data[1]).map((d) => data[1][d]),
            lineStyle: {
              width: 2,
              color: color[1],
            },
          },
        ],
      },
    ],
  };

  return option;
};

export { MDCRadar, CmiBmRadar };
