export const ValueType = {
  Date: { valueType: 'date' },
  _Counted: { valueType: '_Counted' },
};

// columns
export enum ReqActionType {
  ADrgCompositionOfHosp = 'HospDrg/ADrgCompositionOfHosp',
  ADrgCompositionOfCliDept = 'CliDeptDrg/ADrgCompositionOfCliDept',
  SdCompositionOfHosp = 'HospSingleDisease/SdCompositionOfHosp',
  SdCompositionOfCliDept = 'CliDeptSingleDisease/SdCompositionOfCliDept',
  HospDrgsData = 'DrgsDetails/DrgsDetails',
  HospOperData = 'DrgsDetails/OperDetails',
  HospSdData = 'DrgsDetails/SdDetails',
}

// columns front
export const MockColumns = [
  {
    dataIndex: 'RecordId',
    title: '编号',
    visible: true,
  },
  {
    dataIndex: 'OutDept',
    title: 'OutDept',
    visible: true,
  },
  {
    dataIndex: 'PatName',
    title: 'PatName',
    visible: true,
  },
  {
    dataIndex: 'PatNo',
    title: 'PatNo',
    visible: true,
  },
  {
    dataIndex: 'WareHouseNo',
    title: 'WareHouseNo',
    visible: true,
  },
];

// CMI 象限图
const NormalAxisOpts = [
  'PatCnt',
  'Cmi',
  'Cm',
  'DrgCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
  'Cei',
  'Tei',
  'LowRiskPatCnt',
  'LowRiskDeathCnt',
  'LowRiskDeathRatio',
];

export const HospAxisOpts = [...NormalAxisOpts];

export const CliDeptAxisOpts = [...NormalAxisOpts];

export const MedTeamAxisOpts = [...NormalAxisOpts];

export const DoctorAxisOpts = [...NormalAxisOpts];

export const DefaultOpts = {
  xAxis: 'AvgInPeriod',
  yAxis: 'AvgTotalFee',
};

// 病种结构象限图
export const DiseaseTypeDefaultOpts = {
  xAxis: 'AvgRw',
  yAxis: 'AvgTotalFee',
};

const DiseaseTypeNormalAxisOpts = [
  'PatCnt',
  'AvgRw',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
];

export const DiseaseTypeHospAxisOpts = DiseaseTypeNormalAxisOpts;

export const DiseaseTypeCliDeptAxisOpts = DiseaseTypeNormalAxisOpts;

export const DiseaseTypeMedTeamAxisOpts = DiseaseTypeNormalAxisOpts;

export const DiseaseTypeDoctorAxisOpts = DiseaseTypeNormalAxisOpts;

// DRG组象限图
export const GroupTypeDefaultOpts = {
  xAxis: 'PatCnt',
  yAxis: 'AvgTotalFee',
};

const GroupTypeNormalAxisOpts = [
  'PatCnt',
  'AvgInPeriod',
  'AvgTotalFee',
  'AvgMedicineFee',
  'MedicineFeeRatio',
  'AvgMaterialFee',
  'MaterialFeeRatio',
];

export const GroupTypeHospAxisOpts = GroupTypeNormalAxisOpts;

export const GroupTypeCliDeptAxisOpts = GroupTypeNormalAxisOpts;

export const GroupTypeMedTeamAxisOpts = GroupTypeNormalAxisOpts;

export const GroupTypeDoctorAxisOpts = GroupTypeNormalAxisOpts;

export const DiseaseNormalStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'PatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgRw',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
];

export const GroupNormalStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'PatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgRw',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
];

export const DifficultCaseNormalStat = [
  {
    contentData: 'TotalCnt',
    clickable: true,
    footerYoy: true,
    visible: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    args: { HighRw: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'PatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'DrgCnt',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgMedicineFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgMaterialFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
];

export const OperNormalStat = [
  {
    contentData: 'TotalCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
  },
  {
    contentData: 'OperPatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/OperDetails',
  },
  {
    contentData: 'OperPatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Grade3Or4OperPatCnt',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Grade3Or4OperPatRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgPreOperPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgPostOperPeriod',
    clickable: true,
    footerYoy: true,
    visible: true,
  },
];

export const SdCompositionNormalStat = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
    detailsUrl: 'v2/Drgs/DrgsDetails/SdDetails',
  },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MedicineFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'MaterialFeeRatio',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'DeathRatio',
    clickable: true,
    footerYoy: true,
  },
];

export const DeathNormalStat = [
  // 死亡人数
  {
    contentData: 'DeathCnt',
    clickable: true,
    footerYoy: true,

    args: { Dead: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'DeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 手术患者人数、手术患者死亡人数、手术患者死亡率
  {
    contentData: 'SurgeryPatCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'SurgeryDeathCnt',
    clickable: true,
    footerYoy: true,

    args: { Dead: true, IsSurgery: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'SurgeryDeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 围手术期患者人数、围手术期患者死亡人数、围手术期患者死亡率
  {
    contentData: 'PeriOperDeathCnt',
    clickable: true,
    footerYoy: true,

    args: { PeriOperDead: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'PeriOperDeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 手术患者人数、手术患者死亡人数、手术患者死亡率
  {
    contentData: 'OperPatCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'OperDeathCnt',
    clickable: true,
    footerYoy: true,

    args: { IsValidOper: true, Dead: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'OperDeathRatio',
    rightFooterTitle: '死亡率',
  },
  {
    contentData: 'NeonateDeathCnt',
    clickable: true,
    footerYoy: true,
    visible: true,

    args: { Dead: true, Neonate: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'NeonateDeathRatio',
    rightFooterTitle: '死亡率',
  },
  {
    contentData: 'CancerCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'CancerDeathCnt',
    clickable: true,
    footerYoy: true,
    visible: true,

    args: { Dead: true, Cancer: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'CancerDeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 低风险人数，低风险死亡人数，低风险死亡率
  {
    contentData: 'LowRiskPatCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'LowRiskDeathCnt',
    clickable: true,
    footerYoy: true,
    visible: true,

    args: { Dead: true, LowRisk: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'LowRiskDeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 中低风险人数，中低风险死亡人数，中低风险死亡率
  {
    contentData: 'MiddleLowRiskPatCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'MiddleLowRiskDeathCnt',
    clickable: true,
    footerYoy: true,
    visible: true,

    args: { Dead: true, MiddleLowRisk: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',

    rightFootValue: 'MiddleLowRiskDeathRatio',
    rightFooterTitle: '死亡率',
  },
  // 中高风险人数，中高风险死亡人数，中高风险死亡率
  {
    contentData: 'MiddleHighRiskPatCnt',
    clickable: false,
    footerYoy: true,
  },
  {
    contentData: 'MiddleHighRiskDeathCnt',
    clickable: true,
    footerYoy: true,
    visible: true,
    args: { Dead: true, MiddleHighRisk: true },
    detailsUrl: 'v2/Drgs/DrgsDetails/CompositeDetails',
    rightFootValue: 'MiddleHighRiskDeathRatio',
    rightFooterTitle: '死亡率',
  },
];
