import { Card, Col, Row, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { v4 as uuidv4 } from 'uuid';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import UniEcharts from '@uni/components/src/echarts';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import './index.less';
import { UniTable } from '@uni/components/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import CardWithBtns from '@uni/components/src/cardWithBtns';

const FeeChargesTable = (props) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const [tabItems, setTabItems] = useState([]);
  const [tabActiveKey, setTabActiveKey] = useState(undefined);

  useEffect(() => {
    if (props?.tabData) {
      let tabs = props?.tabData
        ?.filter((i) => i.ChargeTypeMode === props?.chargeTypeMode)
        ?.map((d, i) => {
          let label = globalState?.dictData?.[
            props?.chargeTypeMode === 'Med'
              ? 'MedChargeType'
              : 'StatsChargeType'
          ]?.find((v) => v?.Code === d?.FeeType)?.Name;
          if (i === 0) {
            setTabActiveKey(`${props?.chargeTypeMode}||${d?.FeeType}`);
          }
          return {
            ...d,
            FeeTypeName: label,
            label,
            key: `${props?.chargeTypeMode}||${d?.FeeType}`,
          };
        });
      setTabItems(tabs);
    }
  }, [props?.tabData, props?.chargeTypeMode]);

  useEffect(() => {
    if (tabActiveKey && props?.hisId) {
      getChargeDetailsReq({
        HisId: props?.hisId,
        ChargeTypeMode: props?.chargeTypeMode,
        ChargeType: tabActiveKey?.split('||')?.at(1) ?? tabActiveKey,
      });
      chsBmChargeDetailsDataReq({
        ChargeTypeMode: props?.chargeTypeMode,
        ChargeType: tabActiveKey?.split('||')?.at(1) ?? tabActiveKey,
        GroupCode: props?.chsDrgCode,
      });
    }
  }, [tabActiveKey, props?.chargeTypeMode, props?.chsDrgCode, props?.hisId]);

  // Current GetChargeDetails
  const {
    data: currentChargeDetailsData,
    loading: getChargeDetailsLoading,
    run: getChargeDetailsReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/FundSupervise/LatestCardBundle/GetChargeDetails`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return _.orderBy(res.data, 'Cnt', 'desc');
          } else [];
        }
      },
    },
  );

  // Current GetChargeDetails Columns
  // const {
  //   data: currentChargeDetailsColumnsData,
  //   loading: currentChargeDetailsColumnsLoading,
  //   mutate: mutateChargeDetailsColumnsData,
  //   run: currentChargeDetailsColumnsReq,
  // } = useRequest(
  //   () =>
  //     uniCommonService(`Api/FundSupervise/LatestCardBundle/GetChargeDetails`, {
  //       method: 'POST',
  //       headers: {
  //         'Retrieve-Column-Definitions': 1,
  //       },
  //     }),
  //   {
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return tableColumnBaseProcessor([
  //           { dataIndex: 'ChargeName', visible: true }
  //         ], res.data?.Columns);
  //       }
  //     },
  //   },
  // );

  //  GetChsBmChargeDetails
  const {
    data: chsBmChargeDetailsData,
    loading: chsBmChargeDetailsDataLoading,
    run: chsBmChargeDetailsDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(`Api/Chs/Benchmark/GetChsBmChargeDetails`, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return _.orderBy(res.data, 'UsageRatio', 'desc');
          } else [];
        }
      },
    },
  );

  // GetChsBmChargeDetails columns
  // const {
  //   data: columnsData,
  //   loading: columnsLoading,
  //   mutate: mutateColumnsData,
  //   run: columnsReq,
  // } = useRequest(
  //   () =>
  //     uniCommonService(`Api/Chs/Benchmark/GetChsBmChargeDetails`, {
  //       method: 'POST',
  //       headers: {
  //         'Retrieve-Column-Definitions': 1,
  //       },
  //     }),
  //   {
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return tableColumnBaseProcessor([], res.data?.Columns);
  //       }
  //     },
  //   },
  // );

  return (
    <Card title="" size="small">
      <Row className="fee_charges_container">
        <Col>
          <Tabs
            size="small"
            tabPosition="left"
            style={{ height: 300 }}
            items={tabItems}
            activeKey={tabActiveKey}
            onTabClick={(key) => {
              setTabActiveKey(key);
            }}
          />
        </Col>
        <Col style={{ flex: 1 }}>
          <Row gutter={[8, 8]}>
            <Col span={12}>
              <h4>
                当前收费项目（共{currentChargeDetailsData?.length || 0}条）
              </h4>
              <UniTable
                id={`drg_fee_anal_detail_now`}
                rowKey="Id"
                scroll={{ y: 250 }}
                loading={getChargeDetailsLoading}
                columns={tableColumnBaseProcessor(
                  [
                    { dataIndex: 'ChargeName', visible: true, title: '项目' },
                    {
                      dataIndex: 'Cnt',
                      visible: true,
                      title: '数量',
                      width: 100,
                    },
                    {
                      dataIndex: 'DetItemFeeSumamt',
                      visible: true,
                      title: '金额',
                      width: 150,
                    },
                  ],
                  [],
                )}
                pagination={false}
                dataSource={currentChargeDetailsData}
              />
            </Col>
            <Col span={12}>
              <h4>历史收费项目（共{chsBmChargeDetailsData?.length || 0}条）</h4>
              <UniTable
                id={`drg_fee_bm_detail_now`}
                rowKey="Id"
                scroll={{ y: 250 }}
                loading={chsBmChargeDetailsDataLoading}
                columns={tableColumnBaseProcessor(
                  [
                    { dataIndex: 'ChargeName', visible: true, title: '项目' },
                    { dataIndex: 'Quantity', visible: true, title: '历史数量' },
                    { dataIndex: 'Amount', visible: true, title: '历史金额' },
                    {
                      dataIndex: 'UsageRatio',
                      visible: true,
                      title: '使用比例',
                      dataType: 'Percent',
                    },
                  ],
                  [],
                )}
                pagination={false}
                dataSource={chsBmChargeDetailsData}
              />
            </Col>
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default FeeChargesTable;
