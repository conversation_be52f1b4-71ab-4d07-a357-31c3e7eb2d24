import { Card, Col, Row, Tabs, Slider, InputNumber, Input } from 'antd';
import _ from 'lodash';
import { useEffect, useState, useRef } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import CustomGradientChart from '../customGradientChart/index';
import UniTable from '@uni/components/src/table';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import CardEchart from '@uni/components/src/cardEchart';
import { FeeChargeDistributioByMedChrgitmLabelLinePieOption } from './echarts.opts';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import CardWithBtns from '@uni/components/src/cardWithBtns';
import './index.less';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import Stats from '@/components/stats';
import { GrpTotalStatsColumns } from './constants';

export interface IGradientChartAndTableAndPieProps {
  args?: {
    type?: string; // 页面类型1，用于判断selectedItem内容如何入参调api。有：dept majorPerfDept drg adrg medTeam，
    level?: string; // 页面类型2，用于判断globalState内容如何入参调api。有 dept medTeam majorPerfDept
    api?: string;
    columns?: any[]; // 本地columns
    category?: string; // 象限图的category
    axisOpts?: any; // 象限图的x,y轴的下拉框opts
    defaultAxisOpt?: any; // 象限图的x,y轴的默认值
    title?: string; // 导出 & card title
    clickable?: boolean; // table能不能点击下钻
    emitter?: string; // table点击事件触发的eventconstants 不填默认是EventConstant.TABLE_ROW_CLICK
    ref?: any; // react useRef
    detailsTitle?: string; // 目前无用
    mouseEvent?: any; // 象限图的mouseEvent
  };
}

const GradientChartAndTableAndPie = (
  props: IGradientChartAndTableAndPieProps,
) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MajorPerfDepts, CliDepts, insurType, MedTeams } =
    globalState?.searchParams;
  const [settleCompStatsByEntityData, setSettleCompStatsByEntityData] =
    useState<any>([]);
  const [selectedTableItem, setSelectedTableItem] = useState(undefined);

  const [tableParams, setTableParams] = useState(undefined);

  const [range, setRange] = useState([0, 99]);
  const [searchValue, setSearchValue] = useState(undefined);
  const handleSliderChange = (value) => {
    setRange(value);
  };
  const handleInputChange = (index, value) => {
    const newRange = [...range];
    newRange[index] = value;
    setRange(newRange);
  };

  // table click
  useEffect(() => {
    // 多tab共存情况下 应该调用的人自行传emitter做区分，内部不做区分
    if (props?.args?.emitter) {
      Emitter.on(props?.args?.emitter, ({ record, index }) => {
        setSelectedTableItem(record);
      });
      return () => {
        Emitter.off(props?.args?.emitter);
      };
    } else {
      Emitter.on(EventConstant.TABLE_ROW_CLICK, ({ record, index }) => {
        // 没 type的情况 只有一个默认emitter
        setSelectedTableItem(record);
      });
      return () => {
        Emitter.off(EventConstant.TABLE_ROW_CLICK);
      };
    }
  }, [selectedTableItem, props?.args?.type]);

  const GetParams = (selectedItem, type) => {
    let params = {};
    if (!type) return params;

    if (type === 'adrg') {
      params = {
        ...params,
        VersionedADrgCodes: selectedItem?.VersionedADrgCode
          ? [selectedItem?.VersionedADrgCode]
          : [],
      };
    } else if (type === 'drg') {
      params = {
        ...params,
        VersionedChsDrgCodes: [selectedItem?.VersionedChsDrgCode],
      };
    } else if (type === 'dept') {
      params = {
        ...params,
        CliDepts: selectedItem?.CliDept ? [selectedItem?.CliDept] : undefined,
      };
    } else if (type === 'medTeam') {
      params = {
        ...params,
        MedTeams: selectedItem?.MedTeam ? [selectedItem?.MedTeam] : undefined,
      };
    } else if (type === 'majorPerfDept') {
      params = {
        ...params,
        MajorPerfDepts: selectedItem?.MajorPerfDept
          ? [selectedItem?.MajorPerfDept]
          : undefined,
      };
    }
    return params;
  };

  // 整合params
  useEffect(() => {
    if (dateRange?.length && hospCodes?.length) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      if (props?.args?.level === 'dept') {
        if (CliDepts?.length) {
          tableParams = {
            ...tableParams,
            CliDepts,
          };
        }
      } else if (props?.args?.level === 'medTeam') {
        if (MedTeams?.length) {
          tableParams = {
            ...tableParams,
            MedTeams,
          };
        }
      } else if (props?.args?.level === 'majorPerfDept') {
        if (MajorPerfDepts?.length) {
          tableParams = {
            ...tableParams,
            MajorPerfDepts,
          };
        }
      }
      setTableParams(tableParams);
    }
  }, [
    insurType,
    dateRange,
    hospCodes,
    MajorPerfDepts,
    CliDepts,
    MedTeams,
    props?.args?.level,
  ]);

  // 整合 data req
  useEffect(() => {
    if (tableParams) {
      getSettleCompStatsByEntityReq(tableParams);
    }
  }, [tableParams]);

  useEffect(() => {
    if (tableParams && selectedTableItem) {
      // getFeeChargeDistributionReq({
      //   ...tableParams,
      //   ...GetParams(selectedTableItem, props?.args?.type),
      // });
    }
  }, [selectedTableItem, tableParams, props?.args?.type]);

  // columns req
  useEffect(() => {
    if (props?.args?.api) {
      if (!settleCompStatsByEntityColumnsData?.length)
        getSettleCompStatsByEntityColumnsReq();
      // if (!feeChargeDistributionColumnsData?.length)
      //   getFeeChargeDistributionColumnsReq();
    }
  }, [props?.args?.api]);

  const {
    loading: getSettleCompStatsByEntityLoading,
    run: getSettleCompStatsByEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.args?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 做容错 有些返回的用Stats包裹，有些则是直接用arr
          setSettleCompStatsByEntityData(res?.data?.Stats ?? res?.data);
          // 还要判断
          if (
            props?.args?.columns &&
            props?.args?.columns?.findIndex(
              (d) => d?.defaultSortOrder === 'descend',
            ) > -1
          ) {
            // 同时给一个值 因为排序了..
            setSelectedTableItem(
              _.maxBy(res?.data?.Stats ?? res?.data, 'PatCnt'),
            );
          } else {
            setSelectedTableItem(res.data.Stats?.at(0) ?? res?.data?.at(0));
          }
          // 目前没用到
          return res.data;
        }
      },
    },
  );

  // Columns
  const {
    data: settleCompStatsByEntityColumnsData,
    loading: getSettleCompStatsByEntityColumnsLoading,
    mutate: mutateSettleCompStatsByEntityColumns,
    run: getSettleCompStatsByEntityColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(props?.args?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor(
            props?.args?.columns,
            res.data?.Columns,
          );
        }
      },
    },
  );

  // // 费用组成 用于pie图
  // const {
  //   data: feeChargeDistributionData,
  //   loading: getFeeChargeDistributionLoading,
  //   run: getFeeChargeDistributionReq,
  // } = useRequest(
  //   (data) =>
  //     uniCommonService(
  //       `Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`,
  //       {
  //         method: 'POST',
  //         data: data,
  //       },
  //     ),
  //   {
  //     manual: true,
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         if (res?.data?.Stats?.length) {
  //           return res.data;
  //         }
  //       }
  //       return null;
  //     },
  //   },
  // );

  // // 费用组成 Columns
  // const {
  //   data: feeChargeDistributionColumnsData,
  //   loading: getFeeChargeDistributionColumnsLoading,
  //   run: getFeeChargeDistributionColumnsReq,
  // } = useRequest(
  //   () =>
  //     uniCommonService(
  //       `Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`,
  //       {
  //         method: 'POST',
  //         headers: {
  //           'Retrieve-Column-Definitions': 1,
  //         },
  //       },
  //     ),
  //   {
  //     manual: true,
  //     formatResult: (res: RespVO<any>) => {
  //       if (res?.code === 0 && res?.statusCode === 200) {
  //         return tableColumnBaseProcessor(
  //           FeeChargeDistributionColumns,
  //           res.data?.Columns,
  //         );
  //       }
  //     },
  //   },
  // );

  // 外部props.args.type转成要给stats的level
  // dept drg adrg medTeam
  const statsLevelHandler = () => {
    if (props?.args?.type === 'drg') {
      return 'grp';
    }
    return props?.args?.type;
  };

  const [filteredTableData, setFilteredTableData] = useState<any[]>([]);

  // handleSearch
  const handleSearch = (value) => {
    setSearchValue(value);
  };

  useEffect(() => {
    // drg 类型的table data 特殊处理 因为有slider & 搜索框
    if (props?.args?.type === 'drg' && settleCompStatsByEntityData) {
      if (searchValue) {
        setFilteredTableData(
          settleCompStatsByEntityData
            ?.filter((item) => item?.Cw >= range[0] && item?.Cw <= range[1])
            ?.filter(
              (item) =>
                (item?.ChsDrgCode || '')
                  ?.toLowerCase()
                  ?.includes(searchValue?.toLowerCase()) ||
                pinyinInitialSearch(item?.ChsDrgCode, searchValue) ||
                (item?.ChsDrgName || '')
                  ?.toLowerCase()
                  ?.includes(searchValue?.toLowerCase()) ||
                pinyinInitialSearch(item?.ChsDrgName, searchValue),
            ),
        );
      } else {
        setFilteredTableData(
          settleCompStatsByEntityData?.filter(
            (item) => item?.Cw >= range[0] && item?.Cw <= range[1],
          ),
        );
      }
    } else {
      // 其他类型直接插
      setFilteredTableData(settleCompStatsByEntityData || []);
    }
  }, [props?.args?.type, searchValue, settleCompStatsByEntityData, range]);

  // echarts click
  useEffect(() => {
    if (props?.args?.mouseEvent) {
      Emitter.on(
        props?.args?.mouseEvent?.emitter ??
          EventConstant.ECHARTS_MOUSE_EVENT_CLK,
        ({ event, type, id }) => {
          // 象限图点击
          if (
            id ===
              `${props?.args?.level || ''}${
                props?.args?.type || ''
              }BubbleGradient` &&
            filteredTableData?.length > 0
          ) {
            // 定位table & selected
            setSearchValue(event?.data?.ChsDrgName);
            setSelectedTableItem(event?.data);
          }
        },
      );
      return () => {
        Emitter.off(
          props?.args?.mouseEvent?.emitter ??
            EventConstant.ECHARTS_MOUSE_EVENT_CLK,
        );
      };
    }
  }, [filteredTableData, props?.args?.mouseEvent]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <CardWithBtns
          content={
            // 注意：这里有多个echarts需要点击的话 因为也在tab下，所以也应该传一个emitter进来
            <CustomGradientChart
              id={`${props?.args?.level || ''}${
                props?.args?.type || ''
              }BubbleGradient`}
              height={250}
              category={props?.args?.category}
              axisOpts={props?.args?.axisOpts}
              defaultAxisOpt={props?.args?.defaultAxisOpt || {}}
              loading={getSettleCompStatsByEntityLoading}
              dataSource={settleCompStatsByEntityData}
              mouseEvents={props?.args?.mouseEvent}
            />
          }
          needExport={true}
          exportTitle={props?.args?.title}
          exportData={settleCompStatsByEntityData}
          exportColumns={settleCompStatsByEntityColumnsData}
          needModalDetails={true}
          onRefresh={() => {
            getSettleCompStatsByEntityReq(tableParams);
          }}
        />
      </Col>
      <Col xs={24} sm={24} md={24} lg={24} xl={24}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={24} lg={16} xl={16}>
            <CardWithBtns
              content={
                <>
                  {/* drg 特有 slider */}
                  {props?.args?.type === 'drg' && (
                    <Row gutter={0} align="middle" className="mb-1">
                      <Col
                        span={18}
                        style={{ display: 'flex', alignItems: 'center' }}
                      >
                        <span style={{ marginRight: '8px' }}>RW范围：</span>
                        <Slider
                          range
                          min={0}
                          max={99}
                          step={0.01}
                          marks={{
                            0: '0',
                            5: '5',
                            10: '10',
                            20: '20',
                            50: '50',
                            99: '99',
                          }}
                          value={range as [number, number]}
                          onChange={handleSliderChange}
                          style={{ width: '50%', marginRight: '1rem' }}
                        />
                        <InputNumber
                          size="small"
                          style={{ width: '70px', marginRight: '8px' }}
                          min={0}
                          max={99}
                          value={range[0]}
                          step={0.01}
                          onChange={(value) => handleInputChange(0, value)}
                        />
                        <InputNumber
                          size="small"
                          style={{ width: '70px' }}
                          min={0}
                          max={99}
                          step={0.01}
                          value={range[1]}
                          onChange={(value) => handleInputChange(1, value)}
                        />
                      </Col>
                      <Col span={6}>
                        {/* search */}
                        <Input.Search
                          className="search-input-drp"
                          placeholder="病组编码或名称"
                          allowClear
                          value={searchValue}
                          onChange={(e) => setSearchValue(e.target.value)}
                          onSearch={(value) => handleSearch(value)}
                        />
                      </Col>
                    </Row>
                  )}
                  <UniTable
                    onRow={(record, index) => ({
                      onClick: (event) => {
                        if (props?.args?.clickable) {
                          Emitter.emit(
                            props?.args?.emitter ??
                              EventConstant.TABLE_ROW_CLICK,
                            {
                              record,
                              index,
                            },
                          );
                        }
                      },
                    })}
                    loading={getSettleCompStatsByEntityLoading}
                    id={'settle-comp-stats-by-adrg-table'}
                    className={`settle-comp-stats-by-adrg-table ${
                      props?.args?.clickable ? 'clickable' : ''
                    }`}
                    rowKey={'id'}
                    scroll={{
                      x: 'max-content',
                    }}
                    rowClassName={(record, index) => {
                      if (record?.VersionedChsDrgCode)
                        return record.VersionedChsDrgCode ===
                          selectedTableItem?.VersionedChsDrgCode
                          ? 'row-selected'
                          : '';

                      if (record?.VersionedADrgCode)
                        return record.VersionedADrgCode ===
                          selectedTableItem?.VersionedADrgCode
                          ? 'row-selected'
                          : '';

                      if (record?.CliDept)
                        return record?.CliDept === selectedTableItem?.CliDept
                          ? 'row-selected'
                          : '';

                      if (record?.MedTeam)
                        return record?.MedTeam === selectedTableItem?.MedTeam
                          ? 'row-selected'
                          : '';
                    }}
                    dictionaryData={globalState?.dictData}
                    columns={settleCompStatsByEntityColumnsData || []}
                    dataSource={filteredTableData}
                  />
                </>
              }
              needExport={true}
              exportTitle={props?.args?.title}
              exportData={settleCompStatsByEntityData}
              exportColumns={settleCompStatsByEntityColumnsData}
              needModalDetails={true}
              onRefresh={() => {
                getSettleCompStatsByEntityReq(tableParams);
              }}
              columnsEditableUrl={props?.args?.api}
              onColumnChange={(newColumns) => {
                mutateSettleCompStatsByEntityColumns(
                  tableColumnBaseProcessor(props?.args?.columns, newColumns),
                );
              }}
            />
          </Col>
          <Col
            xs={24}
            sm={24}
            md={24}
            lg={8}
            xl={8}
            // style={{ marginTop: '1rem' }}
          >
            {/* 指标 */}
            <Row gutter={[8, 8]} style={{ marginBottom: '8px' }}>
              <Stats
                level={statsLevelHandler()}
                api={`Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsOfGrp`}
                selectedTableItem={selectedTableItem}
                columns={GrpTotalStatsColumns}
                loading={getSettleCompStatsByEntityLoading}
                type="col-xl-8"
                noClickable={true}
              />
            </Row>
          </Col>
        </Row>
      </Col>
    </Row>
  );
};

export default GradientChartAndTableAndPie;
