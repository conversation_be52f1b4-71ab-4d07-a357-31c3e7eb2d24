import theme from '@uni/components/src/echarts/themes/themeBlueYellow';

import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

import _ from 'lodash';

const Gauge = [
  {
    center: ['25%', '21%'],
    data: '全部手术人次占比',
  },
  {
    center: ['75%', '21%'],
    data: '全部三四级手术占比',
  },
  {
    center: ['25%', '71%'],
    data: '区域手术人次占比',
  },
  {
    center: ['75%', '71%'],
    data: '区域三四级手术占比',
  },
];

const OperBmGauge = (data, category = null) => {
  if (_.isEmpty(data)) {
    return {};
  }
  let series = [];
  for (let i = 0; i < 4; i++) {
    series.push({
      type: 'gauge',
      center: Gauge[i].center,
      radius: '35%',
      min: 0,
      max: 1,
      startAngle: 90,
      endAngle: -270,
      splitNumber: 4,
      clockwise: true,
      axisTick: {
        // show: false,'
        // length: -2,
        distance: -13,
        splitNumber: 4,
        lineStyle: {
          width: 2,
          color: '#999',
        },
      },
      splitLine: {
        length: -3,
        distance: -13,
        splitNumber: 4,
        lineStyle: {
          width: 2,
          color: '#999',
        },
      },
      progress: {
        show: true,
        width: 5,
      },
      axisLine: {
        lineStyle: {
          width: 5,
        },
      },
      anchor: {
        show: false,
      },
      pointer: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      data: [
        {
          value: data[0][Gauge[i].data],
          name: Gauge[i].data,
        },
      ],
      title: {
        show: true,
        fontSize: 14,
        offsetCenter: [0, '150%'],
      },
      detail: {
        show: true,
        fontSize: 20,
        offsetCenter: ['0', '0%'],
        formatter: (value) => valueNullOrUndefinedReturnDash(value, 'Percent'),
      },
    });
  }
  let color = theme.color;
  let option = {
    legend: {
      show: true,
    },
    tooltip: {
      trigger: 'axis',
    },
    // dataset: {
    //   source: data,
    // },
    series: series,
  };

  return option;
};

const OperTrendsBar = (data, category) => {
  if (_.isEmpty(data)) {
    return {};
  }
  // data = _.sortBy(data, () => {});
  data.sort(function (a, b) {
    return a.MonthDate > b.MonthDate ? 1 : -1;
  });
  let color = theme.color;
  let option = {
    dataset: {
      source: data,
    },
    legend: {
      show: true,
      selected: {
        一级手术: false,
        二级手术: false,
        三级手术: true,
        四级手术: true,
        三四级手术占比: true,
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        minValueSpan: 4,
        showDetail: false,
        startValue: 0, //数值index
        endValue: 9,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 9,
      },
    ],
    xAxis: {
      type: 'category',
    },
    yAxis: [
      {
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
      {
        position: 'right',
      },
    ],
    series: [
      {
        name: '一级手术',
        type: 'bar',
        barWidth: 7,
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[3],
          },
        },
        encode: {
          y: 'Grade1OperPatCnt',
          x: ['MonthDate'],
          tooltip: 'Grade1OperPatCnt',
        },
      },
      {
        name: '二级手术',
        type: 'bar',
        barWidth: 7,
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[1],
          },
        },
        encode: {
          y: 'Grade2OperPatCnt',
          x: ['MonthDate'],
          tooltip: 'Grade2OperPatCnt',
        },
      },
      {
        name: '三级手术',
        type: 'bar',
        barWidth: 7,
        label: {
          normal: { show: true },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[0],
          },
        },
        encode: {
          y: 'Grade3OperPatCnt',
          x: ['MonthDate'],
          tooltip: 'Grade3OperPatCnt',
        },
      },
      {
        name: '四级手术',
        type: 'bar',
        barWidth: 7,
        label: {
          normal: { show: true },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: color[2],
          },
        },
        encode: {
          y: 'Grade4OperPatCnt',
          x: ['MonthDate'],
          tooltip: 'Grade4OperPatCnt',
        },
      },
      {
        name: '三四级手术占比',
        type: 'line',
        tooltip: {
          valueFormatter: (value) => {
            return valueNullOrUndefinedReturnDash(value, 'Percent');
          },
        },
        yAxisIndex: 1,
        lineStyle: {
          normal: {
            type: 'dotted',
            color: color[1],
            width: 2,
          },
        },
        itemStyle: {
          normal: {
            color: color[1],
          },
        },
        encode: {
          x: 'MonthDate',
          y: ['Grade3Or4OperPatRatio'],
          tooltip: ['Grade3Or4OperPatRatio'],
        },
      },
    ],
  };
  return option;
};

export { OperBmGauge, OperTrendsBar };
