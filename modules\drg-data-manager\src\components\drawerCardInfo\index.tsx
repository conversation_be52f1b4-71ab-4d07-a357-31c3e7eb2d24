import CardInfo from '../cardInfo';
import { Drawer } from 'antd';
import DrgHqmsCardInfo from '../drgHqmsCardInfo';

const DrawerCardInfo = (props) => {
  return (
    <Drawer
      open={props?.visible?.hisId}
      width={'calc(100vw - 180px)'}
      onClose={props?.onClose}
      title={'病案详情'}
      zIndex={1001}
    >
      {props?.type === 'DrgAndHqms' ? (
        <DrgHqmsCardInfo {...props?.visible} />
      ) : (
        <CardInfo {...props?.visible} />
      )}
    </Drawer>
  );
};

export default DrawerCardInfo;
