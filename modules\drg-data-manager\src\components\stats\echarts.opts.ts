import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import { echarts } from '@uni/components/src/echarts';
import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import {
  valueNullOrUndefinedReturnDash,
  valueNullOrUndefinedReturnDashWithDictionaryModule,
} from '@uni/utils/src/utils';
import _ from 'lodash';

// 月度变化趋势线图
const StatsSelectedTrendsLineOption = (data, category, selectedItem) => {
  if (_.isEmpty(data)) {
    return {};
  }
  data = _.orderBy(data, 'MonthDate', 'asc');
  let color = theme.color;
  let length = 4;
  let option = {
    dataset: {
      source: data,
    },
    grid: {
      x: '13%',
      x2: '5%',
    },
    legend: {
      icon: 'circle',
      show: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let value = params[0]?.data;
        if (value)
          return (
            `月份：${valueNullOrUndefinedReturnDash(
              value?.MonthDate,
              'Month',
            )}<br /> ` +
            `${selectedItem?.title}：${valueNullOrUndefinedReturnDash(
              value?.[selectedItem?.contentData],
              selectedItem?.dataType,
            )}<br /> ` +
            `去年同期：${valueNullOrUndefinedReturnDash(
              value?.[`${selectedItem?.contentData}Loy`],
              selectedItem?.dataType,
            )}`
          );
      },
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        // endValue: length,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        // endValue: length,
      },
    ],
    xAxis: {
      type: 'category',
      axisLabel: {
        formatter: (value, index) => {
          return valueNullOrUndefinedReturnDash(value, 'Month');
        },
      },
    },
    yAxis: [
      {
        name: `${selectedItem?.title}`,
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
    ],
    series: [
      {
        name: `${selectedItem?.title}`,
        type: 'line',
        // barWidth: 10,
        // symbol: 'none',
        smooth: true,
        label: {
          normal: {
            show: true,
            formatter: (params) => {
              let value = params?.data;
              return `${valueNullOrUndefinedReturnDash(
                value?.[selectedItem?.contentData],
                selectedItem?.dataType,
              )}`;
            },
          },
        },
        // lineStyle: {
        //   normal: {
        //     opacity: 1,
        //     width: 3,
        //     color: color[3],
        //   },
        // },
        lineStyle: {
          width: 5,
          color: {
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 1,
                color: color[0], // 100% 处的颜色
              },
              {
                offset: 0,
                color: color[3], // 0% 处的颜色
              },
            ],
            globalCoord: false, // 缺省为 false
          },
          shadowColor: 'rgba(62,57,107,.14)',
          shadowBlur: 10,
          shadowOffsetY: 20,
        },
        itemStyle: {
          normal: {
            // barBorderRadius: [5, 5, 0, 0],
            color: color[3],
          },
        },
        encode: {
          x: category,
          y: [selectedItem?.contentData],
          tooltip: [selectedItem?.contentData],
        },
      },
      // {
      //   name: `去年同期`,
      //   type: 'line',
      //   smooth: true,
      //   // label: {
      //   //   normal: {
      //   //     show: true,
      //   //     formatter: (params) => {
      //   //       let value = params?.data;
      //   //       return `${valueNullOrUndefinedReturnDash(
      //   //         value?.[`${selectedItem?.contentData}Loy`],
      //   //         selectedItem?.dataType,
      //   //       )}`;
      //   //     },
      //   //   },
      //   // },
      //   lineStyle: {
      //     normal: {
      //       opacity: 1,
      //       width: 3,
      //       color: color[0],
      //       type: 'dashed',
      //     },
      //   },
      //   itemStyle: {
      //     normal: {
      //       color: color[0],
      //     },
      //   },
      //   encode: {
      //     x: category,
      //     y: [`${selectedItem?.contentData}Loy`],
      //     tooltip: [`${selectedItem?.contentData}Loy`],
      //   },
      // },
    ],
  };
  return option;
};

export { StatsSelectedTrendsLineOption };
