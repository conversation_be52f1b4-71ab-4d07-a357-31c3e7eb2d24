import React, { useEffect, useState } from 'react';
import { Alert, Col, Descriptions, Drawer, Row, Spin, Typography } from 'antd';
import { useRequest } from 'umi';
import { UniTable } from '@uni/components/src';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

const { Text } = Typography;

// 文件上传记录的接口
interface UploadResultMaster {
  MasterId: string;
  FileName: string;
  FileSize: number;
  UploadTime: string;
  UploadStatus: string; // 100: 成功, 999: 失败，其他值表示处理中
  ErrorMessage?: string;
  TotalCnt?: number;
  AcceptCnt?: number;
  RejectCnt?: number;
  [key: string]: any;
}

// 上传文件明细记录的接口
interface UploadResultDetail {
  Id: string;
  MasterId: string;
  CardId: string;
  PatientName: string;
  InDate: string;
  OutDate: string;
  Status: number;
  ErrorMessage?: string;
  [key: string]: any;
}

interface ResultDrawerProps {
  visible: boolean;
  dictData: any;
  title: string;
  masterId: string;
  onClose: () => void;
}

const ResultDrawer: React.FC<ResultDrawerProps> = ({
  visible,
  dictData,
  title,
  masterId,
  onClose,
}) => {
  // 获取详情的列定义
  const [detailColumns, setDetailColumns] = useState<any[]>([]);

  // 获取并处理列配置
  useRequest(
    () =>
      uniCommonService('Api/Hqms/HqmsFileImport/GetUploadResultDetails', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: any) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 处理列配置，为SummaryMsg添加省略号
          const columns = tableColumnBaseProcessor([], res.data?.Columns);

          // 查找并修改SummaryMsg列的配置
          const processedColumns = columns.map((col) => {
            if (col.dataIndex === 'SummaryMsg') {
              return {
                ...col,
                ellipsis: true,
              };
            }
            return col;
          });

          setDetailColumns(processedColumns);
          return processedColumns;
        }
        return [];
      },
    },
  );

  // 获取上传详情列表
  // 分页参数
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取上传详情列表
  const {
    run: fetchDetails,
    data: uploadResultDetails,
    loading: detailsLoading,
  } = useRequest(
    (current = pagination.current, pageSize = pagination.pageSize) =>
      uniCommonService('Api/Hqms/HqmsFileImport/GetUploadResultDetails', {
        method: 'POST',
        data: {
          MasterId: masterId,
          DtParam: {
            Draw: current,
            Start: (current - 1) * pageSize,
            Length: pageSize,
          },
        },
      }),
    {
      manual: true,
      // @ts-ignore
      formatResult: (res: any) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          const result = res.data;
          // 更新分页器的总数
          setPagination((prev) => ({
            ...prev,
            total: result.recordsTotal || 0,
          }));

          // 格式化后端数据为UniTable需要的格式
          return {
            Items: result.data || [],
            TotalCount: result.recordsTotal || 0,
          };
        }
        return { Items: [], TotalCount: 0 };
      },
    },
  );

  // 获取特定上传记录的详细信息（带轮询）
  const {
    data: uploadResultMaster,
    loading: masterLoading,
    run: fetchMasterData,
    cancel: cancelPolling,
  } = useRequest(
    () =>
      uniCommonService('Api/Hqms/HqmsFileImport/GetUploadResultMaster', {
        method: 'POST',
        data: { MasterId: masterId },
      }),
    {
      manual: true,
      pollingInterval: 2000, // 每2秒轮询一次
      pollingWhenHidden: false, // 页面隐藏时暂停轮询
      // @ts-ignore
      formatResult: (res: any) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data) => {
        // 状态是处理完成，获取详情，不再轮询
        if (data?.UploadStatus === '100') {
          cancelPolling(); // 停止轮询
          fetchDetails(pagination.current, pagination.pageSize); // 加载详情数据
        }
        // 状态是失败，停止轮询
        else if (data?.UploadStatus === '999') {
          cancelPolling();
        }
        // 其他状态继续轮询
      },
      pollingErrorRetryCount: 3, // 轮询错误重试次数
    },
  );

  // 当抽屉打开时开始轮询，关闭时取消轮询
  useEffect(() => {
    if (visible && masterId) {
      fetchMasterData(); // 开始轮询
    }

    return () => {
      // 组件卸载或抽屉关闭时取消轮询
      cancelPolling();
    };
  }, [visible, masterId]);

  console.log('dictData', dictData);

  // 渲染Drawer内容
  const renderDrawerContent = () => {
    // 数据完全未加载
    if (!uploadResultMaster) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin />
          <div style={{ marginTop: 16 }}>正在加载数据，请稍候...</div>
        </div>
      );
    }

    // 处理错误情况
    if (uploadResultMaster.UploadStatus === '999') {
      return (
        <>
          <div style={{ marginBottom: 16 }}>
            <Descriptions column={3} bordered>
              <Descriptions.Item label="总记录数">
                {uploadResultMaster.TotalCnt || 0}
              </Descriptions.Item>
              <Descriptions.Item label="接受数量">
                {uploadResultMaster.AcceptCnt || 0}
              </Descriptions.Item>
              <Descriptions.Item label="拒绝数量">
                {uploadResultMaster.RejectCnt || 0}
              </Descriptions.Item>
            </Descriptions>
          </div>

          <Alert
            message="上传处理失败"
            description={uploadResultMaster.ErrorMessage || '未知错误'}
            type="error"
            showIcon
          />
        </>
      );
    }

    // 无论是处理中还是成功，都显示描述部分
    return (
      <>
        <div style={{ marginBottom: 16 }}>
          <Descriptions column={3} bordered>
            <Descriptions.Item label="总记录数">
              {uploadResultMaster.TotalCnt || 0}
            </Descriptions.Item>
            <Descriptions.Item label="接受数量">
              {uploadResultMaster.AcceptCnt || 0}
            </Descriptions.Item>
            <Descriptions.Item label="拒绝数量">
              {uploadResultMaster.RejectCnt || 0}
            </Descriptions.Item>
          </Descriptions>

          {/* 处理中的状态提示 - 当UploadStatus不是100(成功)且不是999(失败)时显示 */}
          {uploadResultMaster.UploadStatus !== '100' &&
            uploadResultMaster.UploadStatus !== '999' && (
              <Alert
                style={{ marginTop: 12 }}
                message="数据处理中"
                description="当前显示的数据可能不是最终结果，处理完成后数据将自动更新。"
                type="info"
                showIcon
              />
            )}
        </div>

        {/* 处理中状态显示加载中，成功状态显示表格 */}
        {masterLoading || uploadResultMaster.UploadStatus !== '100' ? (
          // 处理中状态，显示加载中
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin />
            <div style={{ marginTop: 16 }}>
              正在处理中，请稍候...
              <div>
                <p>文件名: {uploadResultMaster.FileName}</p>
                {/* <p>上传时间: {uploadResultMaster.UploadTime}</p> */}
                <p>
                  处理状态:{' '}
                  {
                    dictData?.['UploadStatus']?.find(
                      (d) => d?.Code === uploadResultMaster.UploadStatus,
                    )?.Name
                  }
                </p>
              </div>
            </div>
          </div>
        ) : (
          // 处理完成状态，显示表格
          <>
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginBottom: 8,
              }}
            >
              <TableColumnEditButton
                {...{
                  columnInterfaceUrl:
                    'Api/Hqms/HqmsFileImport/GetUploadResultDetails',
                  onTableRowSaveSuccess: (columnsData) => {
                    const newColumns = tableColumnBaseProcessor(
                      [],
                      columnsData,
                    );

                    // 保留SummaryMsg的ellipsis属性
                    const finalColumns = newColumns.map((col) => {
                      if (col.dataIndex === 'SummaryMsg') {
                        return { ...col, ellipsis: true };
                      }
                      return col;
                    });

                    setDetailColumns(finalColumns);
                  },
                }}
              />
            </div>

            <UniTable
              id="upload-detail-table"
              rowKey="Id"
              columns={detailColumns || []}
              dataSource={uploadResultDetails?.Items || []}
              loading={detailsLoading}
              dictionaryData={dictData}
              pagination={{
                total: uploadResultDetails?.TotalCount || 0,
              }}
              onChange={(pagination) => {
                setPagination((prev) => ({
                  ...prev,
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                }));
                fetchDetails(pagination.current, pagination.pageSize);
              }}
              scroll={{ x: 'max-content' }}
            />
          </>
        )}
      </>
    );
  };

  return (
    <Drawer
      title={title}
      placement="right"
      width={'calc(100vw - 200px)'}
      onClose={() => {
        // 关闭Drawer时取消轮询并重置状态
        cancelPolling();

        // 重置分页状态
        setPagination({
          current: 1,
          pageSize: 10,
          total: 0,
        });

        // 注意：保留列配置，不清除detailColumns

        // 调用父组件的关闭函数
        onClose();
      }}
      open={visible}
    >
      {renderDrawerContent()}
    </Drawer>
  );
};

export default ResultDrawer;
