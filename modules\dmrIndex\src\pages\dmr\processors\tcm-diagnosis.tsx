import { IcdeOperResp, TcmIcdeResp } from '@/pages/dmr/network/interfaces';
import isNil from 'lodash/isNil';
import {
  icdeItemKeyValueProcessor,
  initializeIcdeItemKeysByPrefix,
} from '@/pages/dmr/processors/diagnosis';
import { v4 as uuidv4 } from 'uuid';
import { icdeExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import {
  filterIdAddAndAllCellEmptyRow,
  icdeOperationTableNoDataAddOne,
} from '@/pages/dmr/processors/processors';
import cloneDeep from 'lodash/cloneDeep';

export const tcmIsHospitalDiagnosisResponseProcessor = (
  formFieldValue: any,
  tcmIcdeItems: TcmIcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'TcmIcdeAdms');
  // 入院诊断
  formFieldValue['TcmIcdeAdmsItem'] = tcmIcdeItems?.TcmIcdeAdms?.at(0);
  Object.keys(tcmIcdeItems?.TcmIcdeAdms?.at(0) ?? {})?.forEach((key) => {
    formFieldValue[`TcmIcdeAdms${key}`] =
      tcmIcdeItems?.TcmIcdeAdms?.at(0)?.[key];
  });
};

export const tcmIsHospitalDiagnosisRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 入院诊断
  if (formFieldValues?.TcmIcdeAdmsIcdeCode) {
    data['CardTcmIcdeAdms'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'TcmIcdeAdms'),
        IcdeSort: 0,
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      });
  } else {
    data['CardTcmIcdeAdms'] = [];
  }
};

export const tcmDiagnosisItemResponseProcessor = (
  formFieldValue: any,
  tcmIcdeItems: TcmIcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'TcmIcdeOtps');
  // 门急诊诊断
  let tcmIcdeOptsItem = tcmIcdeItems?.TcmIcdeOtps?.filter(
    (item) => item?.TcmIcdeCategory === 'B',
  )?.at(0);
  formFieldValue['TcmIcdeOtpsItem'] = tcmIcdeOptsItem;
  Object.keys(tcmIcdeOptsItem ?? {})?.forEach((key) => {
    formFieldValue[`TcmIcdeOtps${key}`] = tcmIcdeOptsItem?.[key];
  });
};

export const tcmDiagnosisItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 门急诊诊断
  if (formFieldValues?.TcmIcdeOtpsIcdeCode) {
    data['CardTcmIcdeOtps'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'TcmIcdeOtps'),
        IcdeSort: 1,
        TcmIcdeCategory: 'B',
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        item['IsMain'] = false;

        return item;
      });
  } else {
    data['CardTcmIcdeOtps'] = [];
  }
};

export const tcmDiagnosisMainItemResponseProcessor = (
  formFieldValue: any,
  tcmIcdeItems: TcmIcdeResp,
) => {
  initializeIcdeItemKeysByPrefix(formFieldValue, 'TcmIcdeOtpsMain');
  // 门急诊诊断
  let TcmIcdeOtpsMainItem = tcmIcdeItems?.TcmIcdeOtps?.filter(
    (item) => item?.TcmIcdeCategory === 'A',
  )?.at(0);
  formFieldValue['TcmIcdeOtpsMainItem'] = TcmIcdeOtpsMainItem;
  Object.keys(TcmIcdeOtpsMainItem ?? {})?.forEach((key) => {
    formFieldValue[`TcmIcdeOtpsMain${key}`] = TcmIcdeOtpsMainItem?.[key];
  });
};

export const tcmDiagnosisMainItemRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  // 门急诊诊断
  if (formFieldValues?.TcmIcdeOtpsMainIcdeCode) {
    data['CardTcmIcdeOtpsMain'] = [
      {
        ...icdeItemKeyValueProcessor(formFieldValues, 'TcmIcdeOtpsMain'),
        IcdeSort: 0,
        TcmIcdeCategory: 'A',
      },
    ]
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      })
      ?.map((item) => {
        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        item['IsMain'] = true;

        return item;
      });
  } else {
    data['CardTcmIcdeOtpsMain'] = [];
  }
};

export const tcmDiagnosisTableResponseProcessor = (
  formFieldValue: any,
  tcmIcdeItems: TcmIcdeResp,
  icdeMetaData?: IcdeOperResp,
) => {
  // 诊断表格
  let icdeTableData = [];

  if (tcmIcdeItems?.TcmIcdeDscgs?.length > 0) {
    for (let item of tcmIcdeItems?.TcmIcdeDscgs) {
      item['id'] = item['IcdeId'] ?? item['Id'];

      let icdeInfoWithExtra = icdeMetaData?.Data?.find(
        (itemWithExtra) => itemWithExtra?.Code === item?.IcdeCode,
      );

      if (icdeInfoWithExtra) {
        // 合并一份出来
        item = {
          ...icdeInfoWithExtra,
          ...item,
        };
      }

      // icdeExtra 处理一下
      item['IcdeExtra'] = Object.keys(icdeExtraMap)?.filter(
        (key) => item?.[key] ?? false,
      );

      icdeTableData.push(item);
    }
  }

  formFieldValue['tcm-diagnosis-table'] = icdeTableData?.sort(
    (a, b) => (a?.IcdeSort ?? 0) - (b?.IcdeSort ?? 0),
  );

  formFieldValue['tcm-diagnosis-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['tcm-diagnosis-table'],
  );

  // 为了form item的刷新
  formFieldValue['tcmDiagnosisTable'] = cloneDeep(
    formFieldValue['tcm-diagnosis-table']?.slice(),
  );
};

export const tcmDiagnosisTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let tcmIcdeTableData = (formFieldValues?.['tcm-diagnosis-table'] || [])
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  if (tcmIcdeTableData?.length === 0) {
    data['CardTcmIcdeDscgs'] = [];
  }

  // 诊断
  if (tcmIcdeTableData?.length > 0) {
    data['CardTcmIcdeDscgs'] = tcmIcdeTableData
      ?.map((item, index) => {
        item['IcdeSort'] = index;

        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        if (index === 0) {
          item['TcmIcdeCategory'] = 'A';
        } else {
          item['TcmIcdeCategory'] = 'B';
        }

        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['IcdeCode']);
        } else {
          return true;
        }
      });
  }
};
