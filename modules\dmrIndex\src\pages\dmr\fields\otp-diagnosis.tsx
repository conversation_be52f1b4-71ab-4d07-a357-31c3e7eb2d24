import { IGridItem } from '@/pages/dmr/interfaces';

export const otpDiagnosis: IGridItem[] = [
  {
    data: {
      prefix: '死亡原因',
      key: 'DeathReason',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
      },
    },
    w: 4,
    md: {
      w: 4,
    },
    sm: {
      w: 8,
    },
    xs: {
      w: 7,
    },
    xxs: {
      w: 6,
    },
  },
  {
    data: {
      prefix: '门（急）诊 诊断编码',
      key: 'IcdeOtpsItem',
      desc: '',
      suffix: '',
      component: 'IcdeSelect',
      props: {
        componentId: 'IcdeOtpsIcdeCode',
        selectFormKey: 'IcdeOtpsIcdeCode',
        itemKey: 'IcdeOtpsItem',
        formKeys: {
          IcdeOtpsIcdeName: 'Name',
          IcdeOtpsIcdeCode: 'Code',
        },
        parentId: 'dmr-content-container',
        icdeSelectType: 'isOtp',
      },
    },
    w: 4,
    md: {
      w: 4,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '名称',
      key: 'IcdeOtpsIcdeName',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
        disabled: true,
      },
    },
    w: 8,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '门（急）诊诊断（中医诊断）',
      key: 'TcmIcdeOtpsMainItem',
      desc: '',
      suffix: '',
      component: 'IcdeSelect',
      props: {
        componentId: 'TcmIcdeOtpsMainIcdeCode',
        selectFormKey: 'TcmIcdeOtpsMainIcdeCode',
        itemKey: 'TcmIcdeOtpsMainItem',
        formKeys: {
          TcmIcdeOtpsMainIcdeName: 'Name',
          TcmIcdeOtpsMainIcdeCode: 'Code',
        },
        parentId: 'dmr-content-container',
        icdeSelectType: 'isTcmMain',
      },
    },
    w: 4,
    md: {
      w: 4,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '名称',
      key: 'TcmIcdeOtpsMainIcdeName',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
        disabled: true,
      },
    },
    w: 8,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
];
