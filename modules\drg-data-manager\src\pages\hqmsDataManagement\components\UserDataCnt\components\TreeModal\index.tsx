import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src/commonService';
import { Divider, Modal, Radio, RadioChangeEvent, Spin, Tree } from 'antd';
import { useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { filterMenuData, treeData } from './data';

function TreeModal({
  modalOpen,
  onModalCancel,
  onModalOk,
}: {
  modalOpen: boolean;
  onModalCancel: () => void;
  onModalOk: (data: any) => void;
}) {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([
    'DrgsMetrics',
    'HqmsMetrics',
    'GradeMetrics',
  ]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

  const [data, setData] = useState([]);

  // 查询data
  const { loading: dataSourceLoading, run: getDataSourceReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetMetricFeatures',
        {
          method: 'POST',
          data: {},
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          const {
            EnableCustomDrg,
            EnableDrgsMetrics,
            EnableGradeMetrics,
            EnableHqmsDrg,
            EnableHqmsMetrics,
          } = response?.data || {};
          const list = filterMenuData(treeData, {
            EnableDrgsMetrics,
            EnableGradeMetrics,
            EnableHqmsMetrics,
            EnableHqmsDrg,
            EnableCustomDrg,
          });
          setData(list);
        }
      },
    },
  );

  useEffect(() => {
    if (modalOpen) getDataSourceReq();
  }, [modalOpen]);

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const onCheck = (checkedKeysValue: string[]) => {
    setCheckedKeys(checkedKeysValue);
  };

  const [value, setValue] = useState(1);

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };

  const onCancel = () => {
    onModalCancel();
    setValue(1);
    setCheckedKeys([]);
  };

  const onOk = async () => {
    const list = checkedKeys?.filter(
      (i) => !['DrgsMetrics', 'HqmsMetrics', 'GradeMetrics']?.includes(i),
    );
    const data = {
      SubFeatures: list,
      UnCalcOnly: value === 1 ? true : false,
    };
    await onModalOk(data);
    setValue(1);
    setCheckedKeys([]);
  };

  return (
    <Modal
      title="分组设置"
      open={modalOpen}
      onCancel={onCancel}
      destroyOnClose={true}
      onOk={onOk}
    >
      {dataSourceLoading ? (
        <Spin spinning={dataSourceLoading} />
      ) : (
        <>
          <Tree
            checkable
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck}
            checkedKeys={checkedKeys}
            selectable={false}
            treeData={data}
          />
          <Divider />
          <Radio.Group onChange={onChange} value={value}>
            <Radio value={1}>分组</Radio>
            <Radio value={2}>仅分组未分组</Radio>
          </Radio.Group>
        </>
      )}
    </Modal>
  );
}

export default TreeModal;
