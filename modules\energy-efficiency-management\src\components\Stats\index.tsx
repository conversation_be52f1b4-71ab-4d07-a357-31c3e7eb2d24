import { Spin, Col, Row, Dropdown, MenuProps, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import SingleStat from '@uni/components/src/statistic';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { CodeValueQuantificationEventConstants } from '@/pages/CodeValueQuantification/constants';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  ForwardOutlined,
} from '@ant-design/icons';
import './index.less';
import { ColTypeSpan } from './constants';
import { useDeepCompareEffect } from 'ahooks';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

export interface IStatsProps {
  // 用于调stats接口与stats数据处理
  // 不调api获取数据（外部已经调用的情况）
  bundledData?: any; // 优先级最高 有这个的话 不会调用api获取数据 只获取columns 并且使用tableParams+extraApiArgs 做 trendsapi 的入参
  loading?: boolean; // 外部loading
  defaultSelectItem?: string; // 默认stat选择的项目 没用大概率是默认空
  // 用外部tableParams + extraApiArgs
  tableParams?: any; // 优先级次高 如果有这个 调用api的searchParams 就使用这个 不使用内部的globalState
  extraApiArgs?: any; // 额外的api参数 如果使用 tableParams 则才使用 extraApiArgs 不然走 selectedTableItem

  // 用内部globalState时会用到的参数（弃用了吧 有点绕了这个东西）
  level?: string; // 用于 判断selectedTableItem的内部 要使用的值的类型：有 grp, adrg, majorPerfDept, dept, medTeam, 不填
  useGlobalState?: boolean; // 不填默认使用selectedTableItem 而 非globalState
  selectedTableItem?: {
    // api 入参
    VersionedADrgCode?: any;
    DrgCode?: any;
    CliDept?: any;
    MedTeam?: any;
    MajorPerfDept?: any;
  };

  api?: string;
  columns?: any[];

  trendApi?: string;
  type?: string; // col-xl-?
  tabKey?: string; // 用于判断当前tab的
  noClickable?: boolean; // 不准点 用于 在XXX波士顿矩阵分析

  chartHeight?: any;

  // 将选中的stat暴露出去
  statSelectedChange?: (record: any) => void;

  // drillDown
  dropdownItems?: MenuProps['items'];
}

const Stats = (props: IStatsProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, MajorPerfDepts, CliDepts, MedTeams } =
    globalState?.searchParams;

  const [settleCompStatsOfEntityData, setSettleCompStatsOfEntityData] =
    useState([]);

  const [statsData, setStatsData] = useState([]);
  const [params, setParams] = useState({});

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectedStatItem, setSelectedStatItem] = useState<any>({});

  // stat click
  useEffect(() => {
    Emitter.on(EventConstant.STAT_CLICK, (record) => {
      setSelectedStatItem(record);
      if (props?.statSelectedChange) {
        props?.statSelectedChange(record);
      }
    });
    return () => {
      Emitter.off(EventConstant.STAT_CLICK);
    };
  }, [selectedStatItem]);

  // 默认选择值
  useEffect(() => {
    if (
      props?.defaultSelectItem &&
      statsData?.length > 0 &&
      isEmptyValues(selectedStatItem)
    ) {
      setSelectedStatItem(
        statsData?.find(
          (data) => data?.MetricName === props?.defaultSelectItem,
        ),
      );
      if (props?.statSelectedChange) {
        props?.statSelectedChange(
          statsData?.find(
            (data) => data?.MetricName === props?.defaultSelectItem,
          ),
        );
      }
    }
  }, [props?.defaultSelectItem, statsData, selectedStatItem]);

  useDeepCompareEffect(() => {
    // 有值
    if (props?.bundledData) {
      setSettleCompStatsOfEntityData(props?.bundledData);
      setParams({ ...props?.tableParams, ...(props?.extraApiArgs || {}) });
    }
    // 不处理params 有tableParams直接用
    else if (!isEmptyValues(props?.tableParams)) {
      if (props?.extraApiArgs) {
        setParams({ ...props?.tableParams, ...props?.extraApiArgs });
        getSettleCompStatsOfEntityReq({
          ...props?.tableParams,
          ...props?.extraApiArgs,
        });
      } else if (props?.level === 'adrg') {
        setParams({
          ...props?.tableParams,
          VersionedADrgCodes: [props?.selectedTableItem?.VersionedADrgCode],
        });
        getSettleCompStatsOfEntityReq({
          ...props?.tableParams,
          VersionedADrgCodes: [props?.selectedTableItem?.VersionedADrgCode],
        });
      } else {
        setParams({
          ...props?.tableParams,
        });
        getSettleCompStatsOfEntityReq({
          ...props?.tableParams,
        });
      }
    }
    // 什么都没有 走globalState / selectItems
    else if (dateRange?.length) {
      let tableParams: any = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
      };

      if (props?.level === 'adrg') {
        if (props?.selectedTableItem?.VersionedADrgCode) {
          tableParams = {
            ...tableParams,
            VersionedADrgCodes: [props?.selectedTableItem?.VersionedADrgCode],
          };
        }
        getSettleCompStatsOfEntityReq(tableParams);
      } else if (props?.level === 'grp') {
        if (props?.selectedTableItem?.DrgCode) {
          tableParams = {
            ...tableParams,
            VersionedDrgCodes: [props?.selectedTableItem?.DrgCode],
          };
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // majorperfdept & dept & medTeam 都有可能出现 ，并且都为必填
      else if (props?.level === 'majorPerfDept') {
        if (props?.useGlobalState) {
          if (MajorPerfDepts?.length) {
            tableParams = {
              ...tableParams,
              MajorPerfDepts,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              MajorPerfDepts: [props?.selectedTableItem?.MajorPerfDept],
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        }
      } else if (props?.level === 'dept') {
        if (props?.useGlobalState) {
          if (CliDepts?.length) {
            tableParams = {
              ...tableParams,
              CliDepts,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              CliDepts: [props?.selectedTableItem?.CliDept],
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        }
      }
      // 医疗组
      else if (props?.level === 'medTeam') {
        if (props?.useGlobalState) {
          if (MedTeams?.length) {
            tableParams = {
              ...tableParams,
              MedTeams,
            };
          }
          getSettleCompStatsOfEntityReq(tableParams);
        } else {
          if (props?.selectedTableItem) {
            tableParams = {
              ...tableParams,
              MedTeams: [props?.selectedTableItem?.MedTeam],
            };
            getSettleCompStatsOfEntityReq(tableParams);
          }
        }
      }
      // 最后啥都没有的话直接调
      else {
        getSettleCompStatsOfEntityReq(tableParams);
      }
      // adrg & grp 只会在selectedTableItem时出现

      setParams(tableParams);
    }
  }, [
    props?.bundledData,
    props?.level,
    dateRange,
    hospCodes,
    MajorPerfDepts,
    CliDepts,
    MedTeams,
    props?.selectedTableItem,
    props?.useGlobalState,
    props?.tableParams,
    props?.extraApiArgs,
  ]);

  // Stats
  const {
    // data: settleCompStatsOfEntityData,
    loading: getSettleCompStatsOfEntityLoading,
    run: getSettleCompStatsOfEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.api, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setSettleCompStatsOfEntityData(res?.data);
        } else {
          setSettleCompStatsOfEntityData([]);
        }
      },
    },
  );

  const {
    data: settleCompStatsOfEntityColumns,
    run: getSettleCompStatsOfEntityColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(props?.api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Columns;
        }
      },
    },
  );

  // Stats数据处理
  useEffect(() => {
    if (
      settleCompStatsOfEntityColumns &&
      settleCompStatsOfEntityColumns.length &&
      settleCompStatsOfEntityData?.length
    ) {
      let data = [];
      // 大改
      // settleCompStatsOfEntityData

      data = settleCompStatsOfEntityData?.map((item) => {
        let contentItemColumn, footItemTitle, footerValue;

        contentItemColumn = props?.columns.find(
          (col) => col.data === item.contentData,
        );

        footItemTitle = '编码前';
        footerValue = item?.preValue;

        console.log('fdsfsdfsa', item, contentItemColumn);
        // 处理 computeValue
        let suffix = undefined;
        if (item?.MetricDirection === 'Positive') {
          if (item?.computeValue !== 0) {
            suffix =
              item?.computeValue > 0 ? (
                // 越大越好
                <span style={{ color: 'green' }}>
                  <ArrowUpOutlined
                    onPointerEnterCapture={() => {}}
                    onPointerLeaveCapture={() => {}}
                  />
                  {valueNullOrUndefinedReturnDash(
                    item?.computeValue,
                    item?.ColumnCustomType || item?.ColumnType,
                    item?.ColumnTitle === 'CMI' ? 4 : 2,
                  )}
                </span>
              ) : (
                <span style={{ color: 'red' }}>
                  <ArrowDownOutlined
                    onPointerEnterCapture={() => {}}
                    onPointerLeaveCapture={() => {}}
                  />
                  {valueNullOrUndefinedReturnDash(
                    Math.abs(item?.computeValue),
                    item?.ColumnCustomType || item?.ColumnType,
                    item?.ColumnTitle === 'CMI' ? 4 : 2,
                  )}
                </span>
              );
          }
        } else if (item?.MetricDirection === 'Negative') {
          if (item?.computeValue !== 0) {
            suffix =
              item?.computeValue > 0 ? (
                <span style={{ color: 'red' }}>
                  <ArrowUpOutlined
                    onPointerEnterCapture={() => {}}
                    onPointerLeaveCapture={() => {}}
                  />
                  {valueNullOrUndefinedReturnDash(
                    item?.computeValue,
                    item?.ColumnCustomType || item?.ColumnType,
                    item?.ColumnTitle === 'CMI' ? 4 : 2,
                  )}
                </span>
              ) : (
                <span style={{ color: 'green' }}>
                  <ArrowDownOutlined
                    onPointerEnterCapture={() => {}}
                    onPointerLeaveCapture={() => {}}
                  />
                  {valueNullOrUndefinedReturnDash(
                    Math.abs(item?.computeValue),
                    item?.ColumnCustomType || item?.ColumnType,
                    item?.ColumnTitle === 'CMI' ? 4 : 2,
                  )}
                </span>
              );
          }
        }

        return {
          ...item,
          title: item?.label,
          value: item?.value,
          dataType: item?.ColumnCustomType || item?.ColumnType,
          clickable: true,
          suffix: suffix,
          // footerNode: item?.preValue ? true : false,
          footerTitle: footItemTitle || '编码前',
          footerValue: item?.preValue ? item?.preValue : 0,
          footerDataType: item?.ColumnCustomType || item?.ColumnType,
          // hidden: !stat?.showGrpCnt && !contentItemColumn?.visible,
        };
      });
      console.log(
        'settleCompStatsOfEntityData',
        settleCompStatsOfEntityData,
        data,
      );
      // setSelectedStatItem(data?.at(0));
      // props?.statSelectedChange(data?.at(0));
      setStatsData(data);
    } else {
      setStatsData([]);
    }
  }, [
    props?.columns,
    settleCompStatsOfEntityColumns,
    settleCompStatsOfEntityData,
  ]);

  // 趋势
  const {
    data: settleCompStatsTrendData,
    loading: getSettleCompStatsTrendLoading,
    run: getSettleCompStatsTrendReq,
  } = useRequest(
    (data) =>
      uniCommonService(props?.trendApi, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.length) {
            return res.data;
          } else return [];
        }
      },
    },
  );

  // 趋势 Columns
  const { data: settleCompStatsTrendColumnsData } = useRequest(
    () =>
      uniCommonService(props?.trendApi, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  useEffect(() => {
    if (Object.keys(params)?.length) {
      // getSettleCompStatsTrendReq(params);
    }
  }, [params]);

  // 最后再处理一次数据
  const [finalStatsData, setFinalStatsData] = useState([]);
  useEffect(() => {
    if (statsData?.length > 0) {
      const groupedData = statsData.reduce((acc, item) => {
        if (!acc[item.headerTitle]) {
          acc[item.headerTitle] = [];
        }
        acc[item.headerTitle].push(item);
        return acc;
      }, {});

      setFinalStatsData(groupedData);

      console.log(';;groupedData', groupedData);
    }
  }, [statsData]);

  return (
    <Col xs={24} sm={24} md={24} lg={12} xl={11}>
      <Spin
        spinning={props?.loading ?? getSettleCompStatsOfEntityLoading ?? false}
      >
        {Object.entries(finalStatsData ?? []).map(([headerTitle, items]) => (
          <>
            <span style={{ display: 'block' }}>{headerTitle}</span>
            <Row gutter={[16, 16]}>
              {items.map((item) => (
                <Col
                  key={item?.dataIndex ?? item?.contentData}
                  xs={ColTypeSpan[props?.type]?.xs}
                  sm={ColTypeSpan[props?.type]?.sm}
                  md={ColTypeSpan[props?.type]?.md}
                  lg={ColTypeSpan[props?.type]?.lg}
                  xl={ColTypeSpan[props?.type]?.xl}
                >
                  <Spin spinning={getSettleCompStatsOfEntityLoading ?? false}>
                    <SingleStat
                      loading={getSettleCompStatsOfEntityLoading}
                      className={`${
                        !props?.noClickable &&
                        selectedStatItem?.MetricName === item?.MetricName
                          ? 'active'
                          : ''
                      }`}
                      {...item}
                      clickable={props?.noClickable ? false : item?.clickable}
                      detailType="chsCardInfo"
                      type="drg"
                      args={{
                        ...item?.args,
                        ...params,
                        id: `drg-settle-single-stats-${props?.level}-${
                          item?.dataIndex ?? item?.contentData
                        }`,
                      }}
                      // title
                      title={
                        <div
                          className="stat-title-container"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <span>{item?.title}</span>
                          {props?.dropdownItems && (
                            <Dropdown
                              menu={{
                                items: props?.dropdownItems,
                                onClick: ({ key }) => {
                                  Emitter.emit(
                                    CodeValueQuantificationEventConstants.STAT_DRILLDOWN_CLICK,
                                    { item, key },
                                  );
                                },
                              }}
                              // placement="bottomLeft"
                              arrow
                            >
                              {/* <IconBtn type='drillDown' style={{ fontSize: '16px', paddingTop: '2px' }} /> */}
                              <Tooltip title="下钻">
                                <ForwardOutlined
                                  onPointerEnterCapture={() => {}}
                                  onPointerLeaveCapture={() => {}}
                                  rotate={90}
                                  className="anticon"
                                  style={{
                                    fontSize: '16px',
                                    paddingTop: '2px',
                                  }}
                                />
                              </Tooltip>
                            </Dropdown>
                          )}
                        </div>
                      }
                    ></SingleStat>
                  </Spin>
                </Col>
              ))}
            </Row>
          </>
        ))}
      </Spin>
    </Col>
  );
};

export default Stats;
