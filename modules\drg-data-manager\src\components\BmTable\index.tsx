import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import UniEcharts from '@uni/components/src/echarts/index';
import { Col, Select, Space, Table, Tabs } from 'antd';
import { CmiTrendsLine } from '@/echarts/cmi.chart.opts';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src/commonService';
import { RespVO } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';
import { useDeepCompareEffect } from 'ahooks';
import { BmBarCharts, BmRadarCharts } from './charts.opts';
import { UniTable } from '@uni/components/src/index';
import _ from 'lodash';
import './index.less';

// TODO
interface IBmTableProps {
  tableParams: any;
  api: string;
  bundleData: any;
  BmChartSelectOptions: any;
  extraApiArgs?: any;
  tableOrderKey?: string;
}

const BmTable = ({
  tableParams,
  api,
  bundleData,
  BmChartSelectOptions,
  extraApiArgs,
  tableOrderKey,
}: IBmTableProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [options, setOptions] = useState([]);

  const [activeKey, setActiveKey] = useState(undefined);

  const [selectedKey, setSelectedKey] = useState(undefined);

  const [areaTableData, setAreaTableData] = useState([]);

  // 医院组标杆值 column api
  const {
    data: ColumnsData,
    loading: getColumnsLoading,
    mutate: mutateColumns,
    run: getColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(api, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 选项处理
          setOptions(
            BmChartSelectOptions?.map((item, index) => {
              let data = res.data?.Columns?.find((col) => col?.data === item);
              if (index === 0) {
                setSelectedKey(item);
              }
              return {
                ...data,
                label: `${data?.title}排名`,
              };
            }),
          );
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  // 医院组标杆值 data api
  const {
    data: Data,
    loading: getDataLoading,
    run: getDataReq,
  } = useRequest(
    (data) =>
      uniCommonService(api, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data?.Results_Bm) {
            setActiveKey(Object.keys(res.data?.Results_Bm).at(0));
          }
          return res.data;
        }
        return null;
      },
    },
  );

  // columns
  useEffect(() => {
    if (api) {
      if (!ColumnsData?.length) getColumnsReq();
    }
  }, [api]);

  // data 如果有extraApiArgs需要判断是不是每个内部值都被赋予 才调
  useDeepCompareEffect(() => {
    if (tableParams) {
      if (!extraApiArgs) {
        getDataReq(tableParams);
      } else {
        if (Object.keys(extraApiArgs)?.every((key) => extraApiArgs[key])) {
          getDataReq({ ...tableParams, ...extraApiArgs });
        }
      }
    }
  }, [tableParams, extraApiArgs]);

  useEffect(() => {
    // 雷达图 全部 使用 Data?.Result_Own 区域 使用 bundleData
    if (bundleData && Data?.Result_Own) {
      let areaObj = {
        HospName: '区域',
      };
      BmChartSelectOptions?.map((item, index) => {
        areaObj[item] = bundleData?.at(0)?.[`${item}BmArea`];
      });
      setAreaTableData([...Data?.Result_Own, areaObj]);
    }
  }, [bundleData, Data?.Result_Own]);

  let tabItems = [
    {
      label: '医院',
      key: 'hosp_bm_analysis',
      children: (
        <Space direction="vertical" style={{ display: 'flex' }}>
          {Data?.Results_Bm &&
            Object.keys(Data?.Results_Bm)?.length > 0 &&
            Object.keys(Data?.Results_Bm)?.map((item) => {
              return (
                <CardWithBtns
                  key={item}
                  title={`${item}标杆值`}
                  content={
                    <Space
                      direction="vertical"
                      size="middle"
                      style={{ display: 'flex' }}
                    >
                      <UniEcharts
                        height={250}
                        elementId="charts"
                        loading={getDataLoading}
                        options={BmBarCharts(
                          ColumnsData,
                          Data?.Results_Bm?.[item],
                          Data?.Result_Own,
                          selectedKey,
                          BmChartSelectOptions,
                        )}
                      />
                      <UniTable
                        id="hosp_bm_table"
                        rowKey={'HospCode' + Math.random()}
                        columns={ColumnsData}
                        dataSource={_.orderBy(
                          [
                            ..._.cloneDeep(Data?.Result_Own || []),
                            ..._.cloneDeep(Data?.Results_Bm?.[item] || []),
                          ],
                          tableOrderKey || 'PatCnt',
                          'desc',
                        )}
                        dictionaryData={globalState?.dictData}
                        widthDetectAfterDictionary
                        scroll={{ x: 'max-content', y: 250 }}
                        pagination={{ position: ['bottomRight'] }}
                        summary={(pageData) => {
                          return (
                            <Table.Summary fixed>
                              <Table.Summary.Row>
                                {ColumnsData?.filter((col) => col.visible)?.map(
                                  (col, index) => {
                                    if (index === 0) {
                                      return (
                                        <Table.Summary.Cell index={0}>
                                          排名
                                        </Table.Summary.Cell>
                                      );
                                    }
                                    return (
                                      <Table.Summary.Cell index={0}>
                                        {
                                          Data?.Rankings?.[item]?.[
                                            col.dataIndex
                                          ]
                                        }
                                      </Table.Summary.Cell>
                                    );
                                  },
                                )}
                              </Table.Summary.Row>
                            </Table.Summary>
                          );
                        }}
                      />
                    </Space>
                  }
                  extra={
                    <Select
                      options={options}
                      fieldNames={{ value: 'data' }}
                      style={{ width: '200px' }}
                      value={selectedKey}
                      onChange={(value) => setSelectedKey(value)}
                    />
                  }
                  needExport={true}
                  exportTitle={'标杆值'}
                  exportData={Data}
                  exportColumns={ColumnsData}
                  needModalDetails={true}
                  onRefresh={() => {
                    if (!extraApiArgs) {
                      getDataReq(tableParams);
                    } else {
                      if (
                        Object.keys(extraApiArgs)?.every(
                          (key) => extraApiArgs[key],
                        )
                      ) {
                        getDataReq({ ...tableParams, ...extraApiArgs });
                      }
                    }
                  }}
                  columnsEditableUrl={api}
                  onColumnChange={(newColumns) => {
                    mutateColumns(tableColumnBaseProcessor([], newColumns));
                  }}
                />
              );
            })}
        </Space>
      ),
    },
    {
      key: 'area_bm_analysis',
      label: '区域',
      children: (
        <CardWithBtns
          title="区域标杆值"
          content={
            <Space
              direction="vertical"
              size="middle"
              style={{ display: 'flex' }}
            >
              <UniEcharts
                height={250}
                elementId="charts"
                loading={getDataLoading}
                options={BmRadarCharts(
                  ColumnsData,
                  bundleData,
                  Data?.Result_Own,
                  BmChartSelectOptions,
                )}
              />
              <UniTable
                id="hosp_bm_table"
                rowKey={'HospCode' + Math.random()}
                columns={ColumnsData?.filter((col) => col?.data !== 'PatCnt')}
                dataSource={areaTableData}
                dictionaryData={globalState?.dictData}
                scroll={{ x: 'max-content' }}
                // pagination={false}
                pagination={{ position: ['bottomRight'] }}
              />
            </Space>
          }
        />
      ),
    },
  ];

  return (
    <Col span={24}>
      <Tabs
        size="small"
        items={tabItems}
        tabPosition="left"
        className="bm-table-tab-sm"
      />
    </Col>
  );
};

export default BmTable;
