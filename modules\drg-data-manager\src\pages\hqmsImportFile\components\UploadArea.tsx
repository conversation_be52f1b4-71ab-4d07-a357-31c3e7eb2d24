import React, { useState } from 'react';
import { useRequest } from 'umi';
import {
  Button,
  Card,
  Col,
  List,
  Modal,
  Progress,
  Row,
  Space,
  Typography,
  Upload,
  message,
} from 'antd';
import {
  DeleteOutlined,
  InboxOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { UniSelect } from '@uni/components/src';

const { Dragger } = Upload;
const { Text } = Typography;

interface UploadAreaProps {
  hospCodes?: string[];
  dictData?: any;
  onUploadSuccess: (masterId: string) => void;
}

const UploadArea: React.FC<UploadAreaProps> = ({
  hospCodes = [],
  dictData,
  onUploadSuccess,
}) => {
  const [selectedHospCode, setSelectedHospCode] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadSuccess, setUploadSuccess] = useState<boolean>(false);
  const [uploadFailed, setUploadFailed] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [currentFileUrl, setCurrentFileUrl] = useState<string | null>(null);

  // 处理文件选择
  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    return false; // 阻止自动上传
  };

  // 自定义上传逻辑
  const customUpload = async () => {
    if (!selectedFile) {
      message.error('请先选择要上传的文件');
      return;
    }

    if (!selectedHospCode) {
      message.error('请选择医院');
      return;
    }

    // 显示上传进度模态框
    setUploadProgress(0);
    setUploadModalVisible(true);
    setUploading(true);

    const formData = new FormData();
    formData.append('HospCode', selectedHospCode);
    formData.append('File', selectedFile);

    try {
      // 使用原生 XMLHttpRequest 来获取进度
      const xhr = new XMLHttpRequest();

      // 监听上传进度
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round(
            (event.loaded / event.total) * 100,
          );
          console.log('Upload progress:', percentComplete);
          setUploadProgress(percentComplete);
        }
      };

      // 请求完成处理
      xhr.onload = () => {
        console.log('xhr', xhr.status, xhr.status >= 200 && xhr.status < 300);
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            console.log('xhr', response);

            // 检查是否有错误格式数据
            if (response?.errors) {
              const errorMessages = Object.values(response.errors)
                .flat()
                .filter(Boolean);

              if (errorMessages.length > 0) {
                throw new Error(errorMessages.join('\n'));
              }
              throw new Error(
                '文件上传出错：' + (response?.title || '未知错误'),
              );
            }
            // 检查文件URL - 可能来自不同路径
            let fileUrl = null;

            // 尝试从新格式中获取
            if (response?.files?.length > 0) {
              fileUrl = response.files[0].url;
            }
            // 尝试从旧格式中获取
            else if (response?.MasterId) {
              fileUrl = response.MasterId;
            }

            if (fileUrl) {
              // 设置上传成功状态，但不关闭模态框
              setUploadSuccess(true);
              setCurrentFileUrl(fileUrl);
              setUploading(false);
              setSelectedFile(null); // 上传成功后清空已选文件
            } else {
              console.error('找不到有效的文件URL', response);
              // 不抛出错误，而是设置失败状态
              setUploadFailed(true);
              setErrorMessage('上传成功但未返回有效的文件标识');
              setUploading(false);
            }
          } catch (error) {
            console.error('解析响应失败:', error);
            // 设置失败状态，显示在当前Modal内
            setUploadFailed(true);
            setErrorMessage(
              error instanceof Error ? error.message : '文件上传失败',
            );
            setUploading(false);
            // 上传失败时不清空文件
          }
        } else {
          console.error('上传请求失败:', xhr.status);
          // 尝试解析错误响应
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            const errorMessages = errorResponse?.errors
              ? Object.values(errorResponse.errors).flat().filter(Boolean)
              : [`上传失败: ${xhr.status}`];

            // 设置失败状态，显示在当前Modal内
            setUploadFailed(true);
            setErrorMessage(errorMessages.join('\n'));
            setUploading(false);
          } catch (e) {
            setUploadFailed(true);
            setErrorMessage(`上传失败: ${xhr.status}`);
            setUploading(false);
          }
        }
      };

      // 错误处理
      xhr.onerror = () => {
        console.error('网络错误');
        // 不关闭Modal，而是显示错误信息
        setUploadFailed(true);
        setErrorMessage('网络错误，请检查您的网络连接后重试');
        setUploading(false);
        // 上传失败时不清空已选文件
      };

      // 初始化请求
      xhr.open(
        'POST',
        `http://${(window as any)?.externalConfig?.ip}:${
          (window as any)?.externalConfig?.port
        }/Api/Hqms/HqmsFileImport/Import`,
        true,
      );
      // FormData不需要设置Content-type，浏览器会自动设置为multipart/form-data
      // 获取请求头（如果需要）
      // 假设你的 token 存储在 localStorage 中
      const token = localStorage.getItem('uni-connect-token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      // 发送请求
      xhr.send(formData);
    } catch (error) {
      console.error('上传过程中出错:', error);
      // 不关闭Modal，而是显示错误信息
      setUploadFailed(true);
      setErrorMessage(
        error instanceof Error ? error.message : '上传过程中出现错误',
      );
      setUploading(false);
      // 上传失败时不清空已选文件
    }
  };

  // 移除已选择的文件
  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  return (
    <Card style={{ marginBottom: 8 }} bodyStyle={{ padding: '12px' }}>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <Space>
              <Text>数据归属：</Text>
              <UniSelect
                placeholder="请选择医院"
                style={{ width: 200 }}
                value={selectedHospCode}
                onChange={(value) => setSelectedHospCode(value)}
                dataSource={dictData?.['Hospital']}
                fieldNames={{
                  label: 'Name',
                  value: 'Code',
                }}
              />
            </Space>

            <div>
              <Dragger
                name="file"
                multiple={false}
                showUploadList={false}
                accept=".csv,.zip"
                beforeUpload={handleFileSelect}
                style={{ padding: '20px 0' }}
              >
                <p className="ant-upload-drag-icon">
                  {/* @ts-ignore */}
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  点击或拖拽文件到此区域选择文件
                </p>
                <p className="ant-upload-hint">
                  仅支持 .csv 或 .zip 格式的文件，一次只能上传一个文件
                  <br />
                  限制最大100MB的文件，一次只接受一个文件
                </p>
              </Dragger>

              {selectedFile && (
                <List
                  size="small"
                  bordered
                  style={{ marginTop: 10 }}
                  dataSource={[selectedFile]}
                  renderItem={(file) => (
                    <List.Item
                      actions={[
                        // @ts-ignore
                        <DeleteOutlined
                          key="delete"
                          onClick={handleRemoveFile}
                          style={{ color: 'red' }}
                        />,
                      ]}
                    >
                      <List.Item.Meta
                        title={file.name}
                        description={`${(file.size / 1024).toFixed(2)} KB`}
                      />
                    </List.Item>
                  )}
                />
              )}

              <Button
                type="primary"
                // @ts-ignore
                icon={<UploadOutlined />}
                style={{ marginTop: 16 }}
                onClick={customUpload}
                disabled={!selectedFile || !selectedHospCode}
                loading={uploading}
              >
                上传
              </Button>
            </div>
          </Space>
        </Col>
      </Row>

      {/* 上传进度模态框 */}
      <Modal
        title={
          uploadSuccess
            ? '文件上传完成'
            : uploadFailed
            ? '上传失败'
            : '文件上传中'
        }
        open={uploadModalVisible}
        footer={null}
        closable={uploadSuccess || uploadFailed}
        maskClosable={uploadSuccess || uploadFailed}
        centered
        onCancel={() => {
          setUploadModalVisible(false);
          setUploadProgress(0);
          setUploadSuccess(false);
          setUploadFailed(false);
          setErrorMessage('');
        }}
      >
        <div style={{ padding: '20px 0' }}>
          <Progress
            percent={uploadProgress}
            status={
              uploadSuccess ? 'success' : uploadFailed ? 'exception' : 'active'
            }
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />

          <div style={{ textAlign: 'center', marginTop: 16 }}>
            {uploadSuccess && '文件上传成功！'}
            {uploadFailed && '上传失败！'}
            {!uploadSuccess && !uploadFailed && '正在上传文件，请稍候...'}
          </div>

          {/* 失败时显示错误信息 */}
          {uploadFailed && (
            <div
              style={{
                marginTop: 16,
                color: '#ff4d4f',
                padding: '8px 12px',
                background: '#fff2f0',
                border: '1px solid #ffccc7',
                borderRadius: '2px',
              }}
            >
              {errorMessage}
            </div>
          )}

          {/* 成功时显示按钮 */}
          {uploadSuccess && (
            <div style={{ textAlign: 'center', marginTop: 20 }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    if (currentFileUrl) {
                      onUploadSuccess(currentFileUrl);
                      setUploadModalVisible(false);
                      setUploadProgress(0);
                      setUploadSuccess(false);
                    }
                  }}
                >
                  查看上传数据导入进度
                </Button>
                <Button
                  onClick={() => {
                    setUploadModalVisible(false);
                    setUploadProgress(0);
                    setUploadSuccess(false);
                  }}
                >
                  关闭
                </Button>
              </Space>
            </div>
          )}

          {/* 失败时显示重试按钮 */}
          {uploadFailed && (
            <div style={{ textAlign: 'center', marginTop: 20 }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    // 重置失败状态，可以重新尝试
                    setUploadFailed(false);
                    setErrorMessage('');
                    // 直接调用上传函数
                    customUpload();
                  }}
                >
                  重新上传
                </Button>
                <Button
                  onClick={() => {
                    setUploadModalVisible(false);
                    setUploadProgress(0);
                    setUploadFailed(false);
                    setErrorMessage('');
                  }}
                >
                  关闭
                </Button>
              </Space>
            </div>
          )}
        </div>
      </Modal>
    </Card>
  );
};

export default UploadArea;
