import { getOperInfoWithStrictMode } from '@/pages/dmr/network/get';
import { operationExtraMap } from '@uni/grid/src/components/icde-oper-input/input';
import {
  filterIdAddAndAllCellEmptyRow,
  icdeOperationTableNoDataAddOne,
} from '@/pages/dmr/processors/processors';
import { IcdeOperResp } from '@/pages/dmr/network/interfaces';
import isNil from 'lodash/isNil';
import { v4 as uuidv4 } from 'uuid';
import cloneDeep from 'lodash/cloneDeep';

export const operationTableResponseProcessor = async (
  formFieldValue,
  operationItems: any[],
  insuranceOperationItems: any[],
  hqmsOperationItems: any[],
  operMetaData?: IcdeOperResp,
) => {
  let operTableData = [];

  if (operationItems?.length > 0) {
    let operInfosWithExtra =
      operMetaData ??
      (
        await getOperInfoWithStrictMode(
          operationItems?.map((item) => item?.OperCode),
        )
      )?.data;

    for (let item of operationItems?.slice()) {
      item['id'] = item['OperId'] ?? item['Id'];

      let operInfoWithExtra = operInfosWithExtra?.Data?.find(
        (itemWithExtra) => itemWithExtra?.Code === item?.OperCode,
      );

      if (operInfoWithExtra) {
        // 合并一份出来
        item = {
          ...operInfoWithExtra,
          ...item,
        };
      }

      // 医保
      let insuranceOperItem = insuranceOperationItems.find(
        (insuranceItem) => insuranceItem?.UniqueId === item.UniqueId,
      );
      if (insuranceOperItem) {
        item['InsurCode'] = insuranceOperItem?.OperCode;
        item['InsurName'] = insuranceOperItem?.OperName;
        item['IsMain'] = insuranceOperItem?.IsMain;
        item['IsReported'] =
          insuranceOperItem?.IsMain == true
            ? true
            : insuranceOperItem?.IsReported;

        // 现在就一个这个有用
        item['InsurIsObsolete'] =
          insuranceOperItem?.insuranceMetaData?.IsObsolete;
      }

      // hqms
      let hqmsOperItem = hqmsOperationItems.find(
        (hqmsItem) => hqmsItem?.UniqueId === item.UniqueId,
      );
      if (hqmsOperItem) {
        item['HqmsCode'] = hqmsOperItem?.OperCode;
        item['HqmsName'] = hqmsOperItem?.OperName;
      }

      // OperExtra 处理一下
      item['OperExtra'] = Object.keys(operationExtraMap)?.filter(
        (key) => item?.[key] ?? false,
      );

      operTableData.push(item);
    }
  }

  // TODO 是否存在 愈合 等级 拆分

  formFieldValue['operation-table'] = operTableData?.sort(
    (a, b) => (a?.OperSort ?? 1) - (b?.OperSort ?? 1),
  );

  formFieldValue['operation-table'] = icdeOperationTableNoDataAddOne(
    formFieldValue['operation-table'],
  );

  // 为了form item的刷新
  formFieldValue['operationTable'] = cloneDeep(
    formFieldValue['operation-table']?.slice(),
  );
};

export const operationTableRequestParamProcessor = (
  formFieldValues: any,
  data: any,
  ignoreNull?: boolean,
) => {
  let operationTableData = (formFieldValues?.['operation-table'] || [])
    ?.slice()
    ?.filter((item) => filterIdAddAndAllCellEmptyRow(item));

  if (operationTableData?.length === 0) {
    data['CardOpers'] = [];
  }

  // 手术
  if (operationTableData?.length > 0) {
    data['CardOpers'] = operationTableData
      ?.map((item, index) => {
        item['OperSort'] = index + 1;

        if (isNil(item['UniqueId'])) {
          item['UniqueId'] = uuidv4();
        }

        return item;
      })
      ?.filter((item) => {
        if (ignoreNull) {
          return !isNil(item['OperCode']);
        } else {
          return true;
        }
      });
  }
};
