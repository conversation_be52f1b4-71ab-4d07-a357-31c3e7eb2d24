import React, { useEffect, useState } from 'react';
import { ExportIconBtn, UniTable } from '@uni/components/src/index';
import { Card, Col, Divider, Row, Space, Tooltip } from 'antd';
import _ from 'lodash';
import { uniCommonService } from '@uni/services/src';
import { useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { generateTableHeaders } from '../../utils';
import { Emitter } from '@uni/utils/src/emitter';
import {
  CodeValueQuantificationEventConstants,
  ELEMENT_CONFIG,
  ELEMENT_TYPES,
  ElementType,
  API_ENDPOINTS,
} from '../../constants';
import { RedoOutlined } from '@ant-design/icons';
import IconBtn from '@uni/components/src/iconBtn/index';
import { exportExcelByClaudeForCode } from '@uni/utils/src/excel-export';

interface GroupItem {
  expr: string;
  name: string;
  [key: string]: any;
}

export interface ITableStatisticProps {
  metricData: any[];
  dictData: any;
  tableParams: any;
  groupItems: GroupItem[];
  needCoder?: boolean;
  needCliDept?: boolean;
  needChief?: boolean;
  needMedTeam?: boolean;
  needMajorPerfDept?: boolean;
  isFromDrawer?: boolean;
  filterMetric?: string;
  nestedCliDept?: string; // 科室二次下钻内容
}

const TableStatistic: React.FC<ITableStatisticProps> = ({
  metricData,
  dictData,
  tableParams,
  groupItems,
  needCoder,
  needCliDept,
  needChief,
  needMajorPerfDept,
  needMedTeam,
  isFromDrawer = false,
  filterMetric,
  nestedCliDept,
}) => {
  const [clkRecord, setClkRecord] = useState<
    Record<ElementType, string | undefined>
  >({
    [ELEMENT_TYPES.CODER]: undefined,
    [ELEMENT_TYPES.CLIDEPT]: undefined,
    [ELEMENT_TYPES.CHIEF]: undefined,
    [ELEMENT_TYPES.MEDTEAM]: undefined,
    [ELEMENT_TYPES.MAJOR_PERF_DEPT]: undefined,
  });

  const [tableColumns, setTableColumns] = useState([]);

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 50, 100],
  });

  const frontTableOnChange = (pagination: any) => {
    setFrontPagination({
      ...frontPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const {
    data: statsChangedData,
    loading: getStatsChangedLoading,
    run: getStatsChangedReq,
    fetches: fetchesStatsChanged,
  } = useRequest(
    (id, data) =>
      uniCommonService(API_ENDPOINTS.GET_STATS, {
        method: 'POST',
        data: data,
      }),
    {
      manual: true,
      fetchKey: (id) => id,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res.data) {
            return res.data?.data;
          }
        }
        return [];
      },
    },
  );

  // 处理下钻动作
  const handleDrillDown = (record: any) => {
    Emitter.emit(
      CodeValueQuantificationEventConstants.STAT_NESTED_DRILLDOWN_CLICK,
      {
        open: true,
        record: record,
        selectedKey: ELEMENT_TYPES.MEDTEAM,
      },
    );
  };

  useEffect(() => {
    if (metricData) {
      const sortedData = _.orderBy(metricData, [
        (item) => (item.MenuSort === 0 ? 1 : 0),
        'MenuSort',
      ]);

      const baseColumns = generateTableHeaders(sortedData, {
        isFromDrawer,
        filterMetric,
        nestedCliDept,
      });

      // 如果是临床科室的统计，添加操作列
      if (needCliDept && isFromDrawer) {
        const columns = [
          ...baseColumns,
          {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            fixed: 'left',
            visible: true,
            align: 'center',
            width: 60,
            render: (text: any, record: any) => (
              <IconBtn
                type={'drillDown'}
                title="下钻到医疗组"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDrillDown(record);
                }}
              />
            ),
          },
        ];
        setTableColumns(columns);
      } else {
        setTableColumns(baseColumns);
      }
    }
  }, [metricData, filterMetric, isFromDrawer, needCliDept, nestedCliDept]);

  useEffect(() => {
    const hasValidParams = tableParams && Object.keys(tableParams)?.length;
    const hasSpecificKey =
      needCoder || needCliDept || needChief || needMedTeam || needMajorPerfDept;

    if (hasValidParams && hasSpecificKey) {
      const statsToFetch = [
        { need: needCoder, key: ELEMENT_TYPES.CODER },
        { need: needCliDept, key: ELEMENT_TYPES.CLIDEPT },
        { need: needChief, key: ELEMENT_TYPES.CHIEF },
        { need: needMedTeam, key: ELEMENT_TYPES.MEDTEAM },
        { need: needMajorPerfDept, key: ELEMENT_TYPES.MAJOR_PERF_DEPT },
      ];

      statsToFetch.forEach(({ need, key }) => {
        if (need) {
          const filteredGroups =
            groupItems?.filter(
              (item) => item?.expr === key || item?.name === key,
            ) || [];

          setFrontPagination({
            ...frontPagination,
            current: 1,
          });

          getStatsChangedReq(key, {
            BasicArgs: {
              ...tableParams,
              CliDepts: nestedCliDept
                ? [nestedCliDept]
                : tableParams?.CliDepts && tableParams?.CliDepts?.length
                ? tableParams?.CliDepts
                : [],
            },
            GrouperCols: filteredGroups,
          });
        }
      });
    }
  }, [
    tableParams,
    groupItems,
    needCoder,
    needCliDept,
    needChief,
    needMajorPerfDept,
    needMedTeam,
    nestedCliDept,
  ]);

  const handleRefresh = (key: ElementType) => (e: React.MouseEvent) => {
    e.stopPropagation();
    const filteredGroups =
      groupItems?.filter((item) => item?.expr === key || item?.name === key) ||
      [];

    setFrontPagination({
      ...frontPagination,
      current: 1,
    });

    getStatsChangedReq(key, {
      BasicArgs: {
        ...tableParams,
        CliDepts: nestedCliDept
          ? [nestedCliDept]
          : tableParams?.CliDepts && tableParams?.CliDepts?.length
          ? tableParams?.CliDepts
          : [],
      },
      GrouperCols: filteredGroups,
    });
  };

  const handleRowClick = (key: ElementType) => (record: any) => {
    setClkRecord({
      ...clkRecord,
      [key]: record[key],
    });
    Emitter.emit(
      CodeValueQuantificationEventConstants.STAT_ROW_CLK + '#' + key,
      record,
    );
  };

  const renderStatCard = (key: ElementType) => {
    const config = ELEMENT_CONFIG[key];
    const need = {
      [ELEMENT_TYPES.CODER]: needCoder,
      [ELEMENT_TYPES.CLIDEPT]: needCliDept,
      [ELEMENT_TYPES.CHIEF]: needChief,
      [ELEMENT_TYPES.MEDTEAM]: needMedTeam,
      [ELEMENT_TYPES.MAJOR_PERF_DEPT]: needMajorPerfDept,
    }[key];

    if (!need) return null;

    const firstColumn = {
      dataIndex: key,
      title: config.title,
      visible: true,
      dictionaryModuleGroup: config.dictModuleGroup,
      dictionaryModule: config.dictModule,
      data: key,
      fixed: 'left',
      exportable: true,
    };

    const iconStyle: React.CSSProperties = {
      width: '32px',
      height: '32px',
      lineHeight: '35px',
      cursor: 'pointer',
    };

    const title = `${config.title}工作量统计`;
    const fileName = `${config.title}工作量统计`;

    return (
      <Col span={24} key={key}>
        <Card
          title={title}
          extra={
            <Space>
              <Divider type="vertical" />
              <Tooltip title="刷新">
                <RedoOutlined
                  className="refresh-icon"
                  style={iconStyle}
                  onClick={handleRefresh(key)}
                  onPointerEnterCapture={() => {}}
                  onPointerLeaveCapture={() => {}}
                />
              </Tooltip>
              <ExportIconBtn
                isBackend={false}
                frontendObj={{
                  columns: [firstColumn, ...tableColumns],
                  dataSource: fetchesStatsChanged?.[key]?.data,
                  dictionaryData: dictData,
                  fileName,
                  customExportFunc: exportExcelByClaudeForCode,
                }}
                btnDisabled={fetchesStatsChanged?.[key]?.data?.length < 1}
              />
            </Space>
          }
        >
          <UniTable
            rowKey={key}
            id={`${key.toLowerCase()}-workload-statistics`}
            bordered
            columns={[firstColumn, ...tableColumns]}
            loading={fetchesStatsChanged?.[key]?.loading ?? false}
            dataSource={fetchesStatsChanged?.[key]?.data}
            onRow={(record) => ({
              onClick: () => handleRowClick(key)(record),
            })}
            rowClassName={(record) =>
              clkRecord[key] === record[key] ? 'ant-table-row-selected' : ''
            }
            forceColumnsUpdate
            widthDetectAfterDictionary
            dictionaryData={dictData}
            scroll={{ x: 'max-content' }}
            pagination={frontPagination}
            onChange={frontTableOnChange}
          />
        </Card>
      </Col>
    );
  };

  return (
    <Row gutter={[16, 16]}>
      {Object.values(ELEMENT_TYPES).map(renderStatCard)}
    </Row>
  );
};

export default TableStatistic;
