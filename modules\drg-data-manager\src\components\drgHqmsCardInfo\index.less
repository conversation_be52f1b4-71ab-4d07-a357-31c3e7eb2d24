@import '~@uni/commons/src/style/variables.less';

@collapse-border: 1px solid @card-border-color;

.card-info-container {
  .ant-collapse {
    background-color: transparent;
    // border: @collapse-border;
  }

  .ant-collapse > .ant-collapse-item > .ant-collapse-header,
  .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }

  .ant-card-head-title {
    .ant-btn-icon-only {
      height: 16px;
    }
  }

  .base-info-descriptions {
    .ant-descriptions-item-content {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .border-right {
    border-right: 1px solid #f0f0f0;
  }

  .hqms-card-list {
    padding: 10px;
    background-color: rgba(@blue-border-color, 0.45);
    .ant-list-item-meta-description {
      color: rgba(0, 0, 0, 0.85);
      font-size: 1.17em;
    }
  }
}

.ant-alert-orange {
  background-color: #fff3e4b5;
  border: 1px solid #ffbd76;
}
