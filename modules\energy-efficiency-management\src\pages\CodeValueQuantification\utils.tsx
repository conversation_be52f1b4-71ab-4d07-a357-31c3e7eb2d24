import { Emitter } from '@uni/utils/src/emitter';
import _ from 'lodash';
import { CodeValueQuantificationEventConstants } from './constants';

function transformData(object) {
  const result = [];

  for (const [key, metrics] of Object.entries(object) as [
    string,
    any[string],
  ][]) {
    const headerTitle = key?.split('/')?.[0];
    const subTitle = key?.split('/')?.[1];

    let preData, diffData, data;

    metrics.forEach((metric) => {
      if (metric?.MetricDirection === 'None') {
        data = metric;
        return;
      }

      if (
        metric.ColumnName.includes('Pre') ||
        metric.MetricName.includes('Pre')
      ) {
        preData = metric;
      } else if (
        metric.ColumnName.includes('Diff') ||
        metric.MetricName.includes('Diff')
      ) {
        diffData = metric;
      } else if (
        !metric.ColumnName.includes('Increment') &&
        !metric.MetricName.includes('Increment') &&
        !metric.ColumnName.includes('Reduction') &&
        !metric.MetricName.includes('Reduction')
      ) {
        data = metric;
      }
    });

    if (data) {
      result.push({
        ...data,
        headerTitle,
        subTitle,
        preData,
        diffData,
      });
    }
  }

  return result;
}

interface GenerateTableHeadersOptions {
  isFromDrawer?: boolean;
  filterMetric?: string;
  nestedCliDept?: string; // 添加科室参数
}

function getBaseMetricName(metricName: string): string {
  return metricName
    .replace(/^(Pre|Post)/, '')
    .replace(/(Cnt|Ratio|Rate|Pat|Increment|Reduction|Diff)$/, '');
}

function areEqual(itemBase, targetBase) {
  const isItemSpecial = ['rw', 'cmi'].includes(itemBase?.toLowerCase());
  const isTargetSpecial = ['rw', 'cmi'].includes(targetBase?.toLowerCase());

  if (isItemSpecial && isTargetSpecial) {
    return true;
  }

  return itemBase === targetBase;
}

function generateTableHeaders(
  data: any[],
  options?: GenerateTableHeadersOptions,
) {
  if (options?.isFromDrawer && options?.filterMetric) {
    const targetItem = data.find(
      (item) =>
        item.MetricName === options.filterMetric ||
        item.ColumnName === options.filterMetric,
    );

    if (targetItem) {
      const targetName = targetItem.MetricName || targetItem.ColumnName;
      const targetBase = getBaseMetricName(targetName);
      const targetPath = targetItem.MenuDirectories?.[0]
        ?.split('/')
        .slice(0, -1)
        .join('/');

      const relatedData = data.filter((item) => {
        if (!item.MenuDirectories?.[0]) return false;

        const itemPath = item.MenuDirectories[0]
          .split('/')
          .slice(0, -1)
          .join('/');
        if (itemPath !== targetPath) return false;

        const itemName = item.MetricName || item.ColumnName;
        const itemBase = getBaseMetricName(itemName);
        return areEqual(itemBase, targetBase);
      });

      return relatedData
        .map((item) => ({
          title: item.MenuDirectories[0].split('/').pop(),
          dataIndex: item.ColumnName,
          key: item.MetricName,
          exportable: true,
          dataType: item.ColumnCustomType || item.ColumnType,
          visible: true,
          menuSort: item.MenuSort,
          orderable: true,
          render: (text: any, record: any) => {
            const isClickable =
              item.ColumnName.includes('Diff') ||
              item.MetricName.includes('Diff') ||
              item.ColumnName.includes('Increment') ||
              item.MetricName.includes('Increment') ||
              item.ColumnName.includes('Reduction') ||
              item.MetricName.includes('Reduction');

            if (!isClickable) return text;

            // 构造事件数据，包含科室信息
            const eventData = {
              ...record,
              MetricName: item.MetricName,
              modalTitle: item.MenuDirectories[0].split('/').join('-'),
              specialData: options.nestedCliDept
                ? {
                    CliDepts: [options.nestedCliDept],
                  }
                : {},
            };

            return (
              <a
                onClick={() =>
                  Emitter.emit(
                    CodeValueQuantificationEventConstants.STAT_NUMBER_CLK,
                    eventData,
                  )
                }
              >
                {text}
              </a>
            );
          },
        }))
        .sort((a, b) => {
          const getOrder = (col: any) => {
            if (col.dataIndex.includes('Pre')) return 1;
            if (col.dataIndex.includes('Increment')) return 3;
            if (col.dataIndex.includes('Reduction')) return 4;
            return 2;
          };
          return getOrder(a) - getOrder(b);
        });
    }

    return [];
  }

  // 非抽屉场景：保持原有的多层表头逻辑
  const headerStructure = {};

  data.forEach((item) => {
    const path = item.MenuDirectories[0].split('/').slice(1);
    let current = headerStructure;

    path.forEach((segment, index) => {
      if (!current[segment]) {
        current[segment] = index === path.length - 1 ? {} : {};
      }
      if (index === path.length - 1) {
        current[segment] = item;
      } else {
        current = current[segment];
      }
    });
  });

  function generateColumns(obj, level = 0) {
    return (Object.entries(obj) as [string, any]).map(([key, value]) => {
      if (_.isObject(value) && value?.ColumnName) {
        const isClickable =
          value.ColumnName.includes('Diff') ||
          value.MetricName.includes('Diff') ||
          value.ColumnName.includes('Increment') ||
          value.MetricName.includes('Increment') ||
          value.ColumnName.includes('Reduction') ||
          value.MetricName.includes('Reduction');

        return {
          title: value?.MenuDirectories[0]?.split('/')?.at(-1),
          exportable: true,
          dataIndex: value?.ColumnName,
          key: value?.MetricName,
          dataType: value?.ColumnCustomType || value?.ColumnType,
          visible: true,
          menuSort: value?.MenuSort,
          orderable: true,
          render: isClickable
            ? (text: any, record: any) => (
                <a
                  onClick={() => {
                    const eventData = {
                      ...record,
                      MetricName: value.MetricName,
                      modalTitle: value.MenuDirectories[0].split('/').join('-'),
                      specialData: options?.nestedCliDept
                        ? {
                            CliDepts: [options.nestedCliDept],
                          }
                        : {},
                    };

                    Emitter.emit(
                      CodeValueQuantificationEventConstants.STAT_NUMBER_CLK,
                      eventData,
                    );
                  }}
                >
                  {text}
                </a>
              )
            : undefined,
        };
      }

      let children = generateColumns(value, level + 1);
      if (children?.at(0)?.dataIndex) {
        children = _.orderBy(children, [
          (item) => (item.MenuSort === 0 ? 1 : 0),
          'MenuSort',
        ]);
      }
      return {
        title: key,
        visible: true,
        exportable: true,
        children,
      };
    });
  }

  const columns = generateColumns(headerStructure);
  return columns.flat();
}

export function getMonthBoundaries(month) {
  // 验证输入格式
  if (!/^\d{4}-\d{2}$/.test(month)) {
    throw new Error('Invalid month format. Expected "YYYY-MM"');
  }

  // 解析年份和月份
  const [year, monthNum] = month.split('-');
  const date = new Date(year, monthNum - 1, 1); // 月份从0开始

  // 获取下个月的第一天，然后减去1天得到当前月的最后一天
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

  // 格式化日期为YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return {
    Sdate: formatDate(date),
    Edate: formatDate(lastDay),
  };
}

export { transformData, generateTableHeaders };
