@import '~@uni/commons/src/style/variables.less';

@progress-label-color: #fff;

.uni-progress-container {
  width: 100%;
  height: 24px;
  position: relative;

  .first-label {
    left: 0%;
  }

  .second-label,
  .third-label {
    border-left: 2px dashed rgb(255 255 255);
  }

  .first-label,
  .second-label,
  .third-label {
    color: @progress-label-color;
    position: absolute;
    padding-left: 5px;
    bottom: 3px;
    font-size: 12px;
  }

  .progress-circle {
    position: absolute;
    top: -2px;
    width: 26px;
    border: 5px solid #fff;
    height: 26px;
    border-radius: 50%;
    background-color: @green-color;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }

  .label-progress-up {
    position: absolute;
    top: -31px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }

  .label-progress-down {
    position: absolute;
    top: 31px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding-left: 4px;
  }

  .label-progress-up span,
  .label-progress-down span {
    white-space: nowrap;
  }

  .box-with-arrow-down::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 7px;
    transform: translateX(-50%) translateY(100%);
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-top: 5px solid @red-color;
  }

  .box-with-arrow-up::after {
    content: '';
    position: absolute;
    bottom: 25px;
    left: 7px;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent @red-color transparent;
    width: 0;
    height: 0;
  }
}
