import React, { useEffect, useMemo, useState } from 'react';
import { useRequest } from 'umi';
import { Card, Divider, Space, Switch } from 'antd';
import { UniSelect, UniTable } from '@uni/components/src';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { Emitter } from '@uni/utils/src/emitter';
import { HqmsUploadRecordColumns } from '../columns';
import { debounce } from 'lodash';
import { EventConstants } from '../constants';

// 文件上传记录的接口
interface UploadResultMaster {
  MasterId: string;
  FileName: string;
  FileSize: number;
  UploadTime: string;
  UploadStatus: number; // 100: 成功, 999: 失败
  ErrorMessage?: string;
  TotalCount?: number;
  SuccessCount?: number;
  FailCount?: number;
  [key: string]: any;
}

interface UploadRecordTabProps {
  onViewDetails: (record: UploadResultMaster) => void;
  dictData?: any;
}

const UploadRecordTab: React.FC<UploadRecordTabProps> = ({
  onViewDetails,
  dictData,
}) => {
  // 筛选条件
  const [filters, setFilters] = useState<{
    hospCode?: string[];
    uploadStatuses?: string[];
    importStatuses?: string[];
    succeed?: boolean;
  }>({});

  // 表格列和数据
  const [columns, setColumns] = useState<any[]>([]);
  const [tableData, setTableData] = useState<UploadResultMaster[]>([]);

  // 获取表格列定义
  useRequest(
    () =>
      uniCommonService('Api/Hqms/HqmsFileImport/GetUploadResultMasters', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      // @ts-ignore
      formatResult: (res: RespVO<TableColumns>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data) => {
        if (data?.Columns) {
          // 将前端columns作为第一个参数传给tableColumnBaseProcessor
          const processedColumns = tableColumnBaseProcessor(
            HqmsUploadRecordColumns,
            data.Columns,
          );

          setColumns(processedColumns);
        }
      },
    },
  );

  // 获取上传记录列表
  const { loading: fetchDataLoading, run: fetchData } = useRequest(
    (params: any) =>
      uniCommonService('Api/Hqms/HqmsFileImport/GetUploadResultMasters', {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
      // @ts-ignore
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return {
            items: res.data || [],
            total: res.data?.TotalCount || 0,
          };
        }
        return { items: [], total: 0 };
      },
      onSuccess: (data) => {
        setTableData(data.items);
      },
      onError: () => {},
    },
  );

  // 使用debounce处理筛选条件变更
  const debouncedFetchData = useMemo(
    () =>
      debounce((filters) => {
        fetchData({
          ...filters,
        });
      }, 300),
    [fetchData],
  );

  // 首次加载数据和筛选条件变更时加载数据
  useEffect(() => {
    debouncedFetchData(filters);
  }, [filters, debouncedFetchData]);

  // 获取字典数据
  const getOptions = (
    dictionaryModule?: string,
    dictionaryModuleGroup?: string,
  ) => {
    if (!dictData) return [];
    let dict = dictionaryModuleGroup
      ? dictData[dictionaryModuleGroup]?.[dictionaryModule]
      : dictData[dictionaryModule];
    console.log('dict', dict);

    return dict || [];
  };

  // 获取状态字典列表
  const getDictionaryByData = (dataField) => {
    const column = columns.find((col) => col.data === dataField);
    return getOptions(column?.dictionaryModule, column?.dictionaryModuleGroup);
  };

  // 获取各种字典
  const uploadStatusesList = useMemo(
    () => getDictionaryByData('UploadStatus'),
    [columns, dictData],
  );
  const importStatusesList = useMemo(
    () => getDictionaryByData('ImportStatus'),
    [columns, dictData],
  );
  const hospitalList = useMemo(
    () => getDictionaryByData('HospCode'),
    [columns, dictData],
  );

  // 监听查看详情事件
  useEffect(() => {
    // 注册查看详情事件
    Emitter.on(EventConstants.HQMS_UPLOAD_RECORD_VIEW_DETAIL, (record) => {
      onViewDetails(record);
    });

    return () => {
      // 清理事件监听
      Emitter.off(EventConstants.HQMS_UPLOAD_RECORD_VIEW_DETAIL);
    };
  }, [onViewDetails]);

  return (
    <Card
      title="上传记录"
      extra={
        <Space>
          <Space size={0}>
            <span>医院：</span>
            <UniSelect
              style={{ width: 150 }}
              placeholder="请选择"
              allowClear
              mode="multiple"
              dataSource={hospitalList}
              fieldNames={{ value: 'Code', label: 'Name' }}
              value={filters.hospCode}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, hospCode: value }))
              }
            />
          </Space>

          <Space size={0}>
            <span>上传状态：</span>
            <UniSelect
              style={{ width: 150 }}
              placeholder="请选择"
              allowClear
              mode="multiple"
              dataSource={uploadStatusesList}
              fieldNames={{ value: 'Code', label: 'Name' }}
              value={filters.uploadStatuses}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, uploadStatuses: value }))
              }
            />
          </Space>

          {/* <Space>
            <span>导入状态：</span>
            <UniSelect
              style={{ width: 150 }}
              placeholder="请选择"
              allowClear
              mode="multiple"
              dataSource={importStatusesList}
              fieldNames={{ value: 'Code', label: 'Name' }}
              value={filters.importStatuses}
              onChange={(value) =>
                setFilters((prev) => ({ ...prev, importStatuses: value }))
              }
            />
          </Space>

          <Space>
            <span>成功导入：</span>
            <Switch
              checked={filters.succeed}
              onChange={(checked) =>
                setFilters((prev) => ({ ...prev, succeed: checked }))
              }
            />
          </Space> */}

          <Divider type="vertical" />

          <TableColumnEditButton
            {...{
              columnInterfaceUrl:
                'Api/Hqms/HqmsFileImport/GetUploadResultMasters',
              onTableRowSaveSuccess: (columnsData) => {
                setColumns(
                  tableColumnBaseProcessor(
                    HqmsUploadRecordColumns,
                    columnsData,
                  ),
                );
              },
            }}
          />
        </Space>
      }
    >
      <UniTable
        id="hqms-upload-record-table"
        rowKey="MasterId"
        columns={columns}
        dataSource={tableData}
        loading={fetchDataLoading}
        dictionaryData={dictData}
        forceColumnsUpdate
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default UploadRecordTab;
