import { Reducer, useEffect, useMemo, useReducer, useState } from 'react';
import { Card, Col, Divider, Row, Space, Spin, Tabs } from 'antd';
import _ from 'lodash';
import { isRespErr, RespType } from '@/utils/widgets';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { Dispatch, useDispatch, useModel, useRequest, useSelector } from 'umi';
import ConfigToImg from '@uni/components/src/config2Img/index';
import {
  InitStatState,
  InitTableState,
  StatAction,
  TableAction,
  statDataReducer,
  tableReducer,
} from '@uni/reducers/src';
import { IReducer, IStatState, ITableState } from '@uni/reducers/src/interface';
import { UniTable } from '@uni/components/src/index';
import { uniCommonService } from '@uni/services/src/commonService';
import DetailsBtn from '@uni/components/src/details-btn/index';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
} from '@uni/utils/src/utils';
import {
  CaretDownOutlined,
  CaretUpOutlined,
  DeploymentUnitOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import SingleStat from '@uni/components/src/statistic/index';
import SingleList from '../SingleList/index';
import { useDeepCompareEffect, useUpdateEffect } from 'ahooks';
import CardWithBtns from '@uni/components/src/cardWithBtns/index';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { NormalStat } from './constants';
import IconBtn from '@uni/components/src/iconBtn/index';

export interface IDrillDownDiseaseStructureProps {
  tableParams?: any; // 如果有这个 就不走内部的searchParams
  selectedTableItem?: {
    //
    VersionedADrgCode?: any;
    VersionedChsDrgCode?: any;
    CliDept?: any;
    MedTeam?: any;
    MajorPerfDept?: any;
  };
  columns?: any[];
  compositionApi?: string;
  dataSource?: any[];
  filterColumns?: any[];
  detailAction?: any;
  label?: string;
}

const DrillDownDiseaseStructure = ({
  tableParams,
  compositionApi,
  columns,
  dataSource,
  filterColumns,
  detailAction,
  label,
}: IDrillDownDiseaseStructureProps) => {
  const [tableState, tableDispatch] = useReducer<
    Reducer<ITableState<any>, IReducer>
  >(tableReducer, InitTableState);

  const [selectedItems, setSelectedItems] = useState(undefined);
  const [statsData, setStatsData] = useState([]);

  const {
    data: ADrgCompositionColumns,
    loading: ADrgCompositionColumnsLoading,
    run: ADrgCompositionColumnsReq,
  } = useRequest(
    () =>
      uniCommonService(compositionApi, {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: RespType) => {
        if (!isRespErr(res)) {
          tableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor([], res.data?.Columns),
            },
          });
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
      },
    },
  );

  const {
    data: ADrgCompositionData,
    loading: ADrgCompositionLoading,
    run: ADrgCompositionReq,
  } = useRequest(
    (data, isDrill = false) =>
      uniCommonService(compositionApi, {
        method: 'POST',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespType) => {
        if (!isRespErr(res)) {
          return res.data;
        }
      },
      onSuccess: (data, params) => {
        if (!params?.at(1)) {
          tableDispatch({
            type: TableAction.dataChange,
            payload: {
              data: _.orderBy(data, 'PatCnt', 'desc'),
              // data,
            },
          });
        } else {
          setSelectedItems(data);
        }
      },
    },
  );

  useEffect(() => {
    if (dataSource) {
      tableDispatch({
        type: TableAction.dataChange,
        payload: {
          data: dataSource,
        },
      });
    } else if (!isEmptyValues(tableParams) && compositionApi) {
      ADrgCompositionReq(tableParams);
    }
  }, [tableParams, compositionApi, dataSource]);

  // table click
  useEffect(() => {
    Emitter.on(EventConstant.TABLE_ROW_CLICK, ({ record, index }) => {
      tableDispatch({
        type: TableAction.clkChange,
        payload: {
          clkItem: record,
        },
      });
    });
    return () => {
      Emitter.off(EventConstant.TABLE_ROW_CLICK);
    };
  }, []);

  // 等table数据出来了，再调用下面的数据显示
  useUpdateEffect(() => {
    if (tableState.clkItem) {
      //   ADrgCompositioByHospnReq({
      //     ...tableParams,
      //     VersionedADrgCodes: tableState?.clkItem?.VersionedADrgCode ? [tableState?.clkItem?.VersionedADrgCode] : [],
      //   });
      //   ADrgCompositioByCliDeptReq({
      //     ...tableParams,
      //     VersionedADrgCodes: tableState?.clkItem?.VersionedADrgCode ? [tableState?.clkItem?.VersionedADrgCode] : [],
      //   });
      // emitter
      // Emitter.emit(
      //   EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK,
      //   tableState.clkItem,
      // );
      // stats 处理
      if (ADrgCompositionColumns && ADrgCompositionColumns.length) {
        let data = [];
        data = NormalStat?.map((stat: any) => {
          let contentItemColumn,
            footItemColumn,
            value,
            footerValue,
            yoy,
            suffix,
            footerNode;

          contentItemColumn = ADrgCompositionColumns.filter(
            (col) => col.data === stat.contentData,
          )[0];

          value = tableState?.clkItem?.[`${stat.contentData}`];
          // yoy
          yoy = tableState?.clkItem?.[`${stat.contentData}Yoy`];

          if (!yoy) {
            suffix = '';
          } else if (yoy > 0) {
            let node = (
              <span>
                <CaretUpOutlined className="font-success" />
                {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
              </span>
            );
            if (stat?.footerYoy) footerNode = node;
            else suffix = node;
          } else {
            let node = (
              <span>
                <CaretDownOutlined className="font-danger" />
                {valueNullOrUndefinedReturnDash(yoy, 'Percent')}
              </span>
            );
            if (stat?.footerYoy) footerNode = node;
            else suffix = node;
          }

          if (stat?.footerData) {
            footItemColumn = ADrgCompositionColumns.filter(
              (col) => col.data === stat.footerData,
            )[0];
            Object.keys(tableState?.clkItem).forEach((key, val) => {
              if (key === stat.footerData) {
                footerValue = val;
              }
            });
          }
          return {
            ...stat,
            title: stat?.title || contentItemColumn?.title,
            value,
            suffix,
            dataType: stat?.dataType || contentItemColumn?.dataType,
            footerNode,
            footerTitle: stat?.footerTitle || footItemColumn?.title,
            footerValue,
            footerDataType: footItemColumn?.dataType,
            hidden: !stat?.showGrpCnt && !contentItemColumn?.visible,
          };
        });

        setStatsData(data);
      } else {
        setStatsData([]);
      }
    }
  }, [tableState.clkItem]);

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <CardWithBtns
          title={label || '病种结构'}
          extra={
            <ConfigToImg
              tableProps={{
                dataSource: tableState.data,
                columns: tableState.columns,
              }}
            />
          }
          content={
            <UniTable
              id="disease_type"
              className="clkable-table-container"
              rowKey="ADrgCode"
              loading={ADrgCompositionLoading ?? false}
              columns={[
                {
                  dataIndex: 'operation',
                  visible: true,
                  width: 40,
                  align: 'center',
                  fixed: 'left',
                  title: '',
                  order: 1,
                  render: (node, record, index) => {
                    return (
                      <Space>
                        <IconBtn
                          title="下钻"
                          type="drillDown"
                          onClick={(e) => {
                            e.stopPropagation();
                            tableDispatch({
                              type: TableAction.clkChange,
                              payload: {
                                clkItem: record,
                              },
                            });
                            Emitter.emit(
                              EventConstant.DISEASE_STRUCTURE_TABLE_ROW_CLICK,
                              record,
                            );
                          }}
                          customIcon={<SearchOutlined />}
                        />
                        <IconBtn
                          type="details"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (detailAction) {
                              detailAction(record);
                            }
                          }}
                        />
                      </Space>
                    );
                  },
                },
                ...(filterColumns?.length > 0
                  ? tableState.columns?.filter((col) =>
                      filterColumns.includes(col.data),
                    )
                  : tableState.columns),
              ]}
              dataSource={tableState.data}
              scroll={{ x: 'max-content' }}
              clickable={true}
              rowClassName={(record, index) => {
                return record?.ADrgCode === tableState?.clkItem?.ADrgCode
                  ? 'row-selected'
                  : '';
              }}
            />
          }
          needExport={true}
          exportTitle={label || '病种结构'}
          exportData={tableState.data}
          exportColumns={tableState.columns}
          needModalDetails={true}
          onRefresh={() => {
            ADrgCompositionReq({ ...tableParams });
          }}
          columnsEditableUrl={compositionApi}
          onColumnChange={(newColumns) => {
            tableDispatch({
              type: TableAction.columnsChange,
              payload: {
                columns: tableColumnBaseProcessor([], newColumns),
              },
            });
          }}
        />
      </Col>
      {/* <Divider></Divider>
      <Col span={24}>
        <h2 className="text-bold-600 mb-1 ml-1">
          <span className="mr-1">
            <DeploymentUnitOutlined />
          </span>
          {tableState?.clkItem?.ADrgName
            ? tableState?.clkItem?.ADrgName
            : '请选择ADRG组'}
        </h2>
      </Col>
      <Col xs={24} sm={24} md={24} lg={24} xl={16}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Row gutter={[16, 16]}>
              {(tableState?.clkItem ? statsData : []).map((d) => {
                if (!d?.hidden || d?.visible) {
                  return (
                    <Col
                      key={d?.dataIndex ?? d?.contentData}
                      xs={12}
                      sm={12}
                      md={12}
                      lg={8}
                      xl={6}
                    >
                      <Spin spinning={ADrgCompositionLoading}>
                        <SingleStat
                          loading={ADrgCompositionLoading}
                          {...d}
                          detailType="chsCardInfo"
                          type="drg"
                        ></SingleStat>
                      </Spin>
                    </Col>
                  );
                }
              })}
            </Row>
          </Col>
        </Row>
      </Col>
      <Col xs={24} sm={24} md={24} lg={24} xl={8}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={24} md={12} lg={12} xl={24}>
            <CardWithBtns
              title={'该病种全院出院人次分布'}
              content={
                <SingleList
                  data={_.map(
                    _.orderBy(ADrgCompositionByHospData, ['PatCnt'], ['desc']),
                    (data, i) => {
                      return {
                        key: i + 1,
                        name: data?.HospName,
                        value: data?.PatCnt,
                      };
                    },
                  )}
                  loading={ADrgCompositionByHospLoading ?? false}
                ></SingleList>
              }
              needExport={true}
              exportTitle={`${tableState?.clkItem?.ADrgName}_该病种全院出院人次分布`}
              exportData={ADrgCompositionByHospData}
              exportColumns={ADrgCompositionByHospColumns}
              needModalDetails={true}
              onRefresh={() => {
                ADrgCompositioByHospnReq({
                  ...tableParams,
                  VersionedADrgCodes: tableState?.clkItem?.VersionedADrgCode ? [tableState?.clkItem?.VersionedADrgCode] : [],
                });
              }}
            />
          </Col>
          <Col xs={24} sm={24} md={12} lg={12} xl={24}>
            <CardWithBtns
              title={'该病种科室出院人次分布(TOP10)'}
              content={
                <SingleList
                  data={_.map(
                    _.orderBy(
                      ADrgCompositioByCliDeptData?.slice(0, 10),
                      ['PatCnt'],
                      ['desc'],
                    ),
                    (data, i) => {
                      return {
                        key: i + 1,
                        name: data?.CliDeptName,
                        value: data?.PatCnt,
                      };
                    },
                  )}
                  loading={ADrgCompositioByCliDeptLoading ?? false}
                ></SingleList>
              }
              needExport={true}
              exportTitle={`${tableState?.clkItem?.ADrgName}_该病种科室出院人次分布(TOP10)`}
              exportData={ADrgCompositioByCliDeptData}
              exportColumns={ADrgCompositioByCliDeptColumns}
              needModalDetails={true}
              onRefresh={() => {
                ADrgCompositioByCliDeptReq({
                  ...tableParams,
                  VersionedADrgCodes: tableState?.clkItem?.VersionedADrgCode ? [tableState?.clkItem?.VersionedADrgCode] : [],
                });
              }}
            />
          </Col>
        </Row>
      </Col> */}
    </Row>
  );
};

export default DrillDownDiseaseStructure;
