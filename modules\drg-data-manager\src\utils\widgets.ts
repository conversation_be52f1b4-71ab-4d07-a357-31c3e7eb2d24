import { ValueType } from '@/constants';
import { ITableState } from '@/interface';
import _ from 'lodash';

export type RespType<T = any> = {
  code?: number;
  requestUrl?: string;
  statusCode?: number;
  message?: string;
  data?: T;
};
// api err
const isRespErr = (res: RespType) => {
  if (!!res.code === false) return false;
  let { code, statusCode } = res;
  if (code === 0 && statusCode === 200) {
    return false;
  }
  return true;
};

// array item move 到顶
const arrElMoveTop = (arr: any[], index: number) => {
  if (index != 0) {
    arr.unshift(arr.splice(index, 1)[0]);
  }
};

// table columns handler
const columnsHandler = (columns, opts = null) => {
  return _.cloneDeep(columns)
    .map((d) =>
      d.data.indexOf('Is') === 0
        ? {
            ...d,
            valueType: 'boolean',
          }
        : {
            ...d,
            valueType: ValueType[d.dataType]?.valueType ?? 'text',
          },
    )
    .concat(opts ?? {});
};

// dyn-ddr part: 1. review status check
const canDynddrReview = (record) => {
  if (record.constructor === Object) {
    return record.Status === '1' || record.Status === '2';
  }
  if (Array.isArray(record)) {
    //TODO
  }
  return false;
};

// dyn-ddr part: 2. sorting
const sortingHandler = (sorters) => {
  return !_.isEmpty(sorters?.order)
    ? `${sorters?.columnKey ?? sorters?.column?.data ?? ''} ${
        sorters?.order === 'ascend' ? 'asc' : 'desc'
      }`
    : undefined;
};

/** 对umi-request进行formdata解析时候数组的处理 */
// data: 要传入的对象，类型obj；keys：要做处理的key名称，就是对象里面是数组的那个键的string
export function forUmiRequestFormData(data, keys: string[] = []) {
  if (!data) return;
  if (Array.isArray(data)) {
    let tempC = data.map((d, i) => ({ [i]: d }));
    return tempC;
  } else {
    let tempD = _.cloneDeep(data);
    if (!keys.length) {
      Object.keys(data).forEach((d) => {
        if (Array.isArray(data[d])) {
          tempD[d] = data[d].map((v, i) => ({ [i]: v }));
        }
      });
    } else {
      keys.forEach((key) => {
        if (tempD[key]?.length > 0) {
          tempD[key] = tempD[key].map((data, index) => {
            return {
              ...data,
              umiRequestIndex: index,
            };
          });
        }
      });
      keys.forEach((key) => {
        if (tempD[key]?.length > 0) {
          let result = _.keyBy(tempD[key], 'umiRequestIndex');
          tempD[key] = result;
          // // console.log(result)
        }
      });
    }

    return tempD;
  }
}

// for dva table发出之前 处理数据
// FIXME TODO 这里的data会不会出问题 ？
const fetchTableParamsHandler = (requestParams) => {
  let res = {};
  if (
    !(requestParams?.params ?? requestParams?.data)?.current ||
    !(requestParams?.params ?? requestParams?.data)?.pageSize
  ) {
    return {
      ...requestParams,
      params: { ...requestParams.params },
      data: { ...requestParams.data },
    };
  }
  // zym
  if (!requestParams?.dataType || requestParams?.dataType === 'normal') {
    res['DtParam'] = {
      Draw: 1,
      Start:
        ((requestParams?.params ?? requestParams?.data)?.current - 1) *
        (requestParams?.params ?? requestParams?.data)?.pageSize,
      Length: (requestParams?.params ?? requestParams?.data)?.pageSize,
      // Columns: tableColumnSearchProcessor(dmrManagementCardColumns, dmrSearchParams),
    };
  }
  // cqj | hyp
  else if (
    requestParams?.dataType === 'dyn-ddr' ||
    requestParams?.dataType === 'mr'
  ) {
    res['SkipCount'] =
      ((requestParams?.params ?? requestParams?.data)?.current - 1) *
      ((requestParams?.params ?? requestParams?.data)?.pageSize ?? 0);
    res['MaxResultCount'] =
      (requestParams?.params ?? requestParams?.data)?.pageSize ?? 20000;
    res['isDynDdr'] = requestParams?.dataType === 'dyn-ddr' ? true : false;
    res['isMr'] = requestParams?.dataType === 'mr' ? true : false;
  }

  // FIXME TODO 这里的data 会不会有问题？

  return {
    ...requestParams,
    ...res,
    params: { ...requestParams.params },
    data: { ...requestParams.data, ...res },
  };
};

export {
  isRespErr,
  arrElMoveTop,
  canDynddrReview,
  columnsHandler,
  sortingHandler,
  fetchTableParamsHandler,
};
