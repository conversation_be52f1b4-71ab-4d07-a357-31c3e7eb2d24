import { Tag } from 'antd';

const transferDictFormat = (dict) => {
  if (dict?.length) {
    return dict.reduce((acc, { Code, Name }) => {
      acc[Code] = { text: Name };
      return acc;
    }, {});
  } else return [];
};

export const BaseInfoColumns = (dict) => {
  return [
    {
      title: '病案号',
      key: 'text',
      dataIndex: 'PatNo',
    },
    {
      title: '姓名',
      key: 'text',
      dataIndex: 'PatName',
    },
    {
      title: '性别',
      key: 'text',
      dataIndex: 'PatSex',
      valueType: 'select',
      valueEnum: dict?.Dmr?.XB?.map((a) => a?.Name) || [],
    },
    {
      title: '年龄',
      key: 'text',
      dataIndex: 'PatAge',
    },

    {
      title: '出生日期',
      key: 'text',
      dataIndex: 'PatBirth',
      valueType: 'date',
    },
    {
      title: '新生儿入院体重',
      key: 'text',
      dataIndex: 'BabyIw',
    },
    {
      title: '入院日期',
      key: 'text',
      dataIndex: 'InDate',
      valueType: 'date',
    },
    {
      title: '出院日期',
      key: 'text',
      dataIndex: 'OutDate',
      valueType: 'date',
    },

    {
      title: '入院途径',
      key: 'text',
      dataIndex: 'AdmissionPath',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Dmr?.RYTJ),
    },
    {
      title: '离院方式',
      key: 'text',
      dataIndex: 'OutType',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Dmr?.LYFS),
    },
    {
      title: '出院科室',
      key: 'text',
      dataIndex: 'CliDept',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.CliDepts),
    },
    {
      title: '医疗组',
      key: 'text',
      dataIndex: 'MedTeam',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.MedTeams),
    },

    {
      title: '科主任',
      key: 'text',
      dataIndex: 'Director',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
    {
      title: '主任(副主任)医师',
      key: 'text',
      dataIndex: 'Chief',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
    {
      title: '主治医师',
      key: 'text',
      dataIndex: 'Attending',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },
    {
      title: '住院医师',
      key: 'text',
      dataIndex: 'Resident',
      valueType: 'select',
      valueEnum: transferDictFormat(dict?.Employee),
    },

    {
      title: '住院天数',
      key: 'text',
      dataIndex: 'InPeriod',
    },
    {
      title: '总费用',
      key: 'text',
      dataIndex: 'TotalFee',
      valueType: 'money',
    },
    {
      title: '材料费',
      key: 'text',
      dataIndex: 'MaterialFee',
      valueType: 'money',
    },
    {
      title: '药品费',
      key: 'text',
      dataIndex: 'MedicineFee',
      valueType: 'money',
    },
  ];
};

export const IcdeColumns = (dictData) => [
  {
    dataIndex: 'IsMain',
    title: '诊断类型',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>
          {record?.['IsMain'] ? (
            <Tag color="error" style={{ borderColor: 'transparent' }}>
              主诊
            </Tag>
          ) : (
            `次诊${record?.['IcdeSort'] || ''}`
          )}
        </span>
      );
    },
  },
  {
    dataIndex: 'IcdeCode',
    title: '诊断编码',
    visible: true,
    width: 150,
    align: 'center',
  },
  {
    dataIndex: 'IcdeName',
    title: '诊断名称',
    visible: true,
    // width: 150,
    align: 'center',
  },
  {
    dataIndex: 'IcdeCond',
    title: '入院病情',
    visible: true,
    align: 'center',
    width: 80,
    render: (text) => {
      return dictData?.Dmr?.RYBQ?.find((d) => d?.Code === text)?.Name ?? text;
    },
  },
];

export const OperColumns = [
  {
    dataIndex: 'IsMain',
    title: '手术类型',
    visible: true,
    width: 80,
    align: 'center',
    render: (node, record, index) => {
      return (
        <span>
          {record?.['IsMain'] ? (
            <Tag color="error" style={{ borderColor: 'transparent' }}>
              主手术
            </Tag>
          ) : (
            `其他手术${record?.['OperSort'] - 1 || ''}`
          )}
        </span>
      );
    },
  },
  {
    dataIndex: 'OperCode',
    title: '手术编码',
    visible: true,
    width: 150,
    align: 'center',
  },
  {
    dataIndex: 'OperName',
    title: '手术名称',
    visible: true,
    width: 200,
    align: 'center',
  },
  {
    dataIndex: 'OprnOprtBegntime',
    title: '手术日期',
    dataType: 'Date',
    visible: true,
    align: 'center',
    width: 120,
  },
  {
    dataIndex: 'WoundRateClass',
    title: '切口等级',
    visible: true,
    align: 'center',
    width: 80,
  },
  {
    dataIndex: 'HealingRateClass',
    title: '愈合等级',
    visible: true,
    align: 'center',
    width: 80,
  },
  {
    dataIndex: 'Operator',
    title: '主刀',
    dictionaryModule: 'Employee',
    visible: true,
    width: 80,
    align: 'center',
  },
  {
    dataIndex: 'Firstasst',
    dictionaryModule: 'Employee',
    title: 'Ⅰ助',
    visible: true,
    width: 80,
    align: 'center',
  },
  {
    dataIndex: 'Secondasst',
    dictionaryModule: 'Employee',
    title: 'Ⅱ助',
    visible: true,
    width: 80,
    align: 'center',
  },
];

export const DrgListColumns = (data) => {
  return [
    {
      title: 'DRG组代码',
      key: 'DrgCode',
    },
    {
      title: 'DRG组名称',
      key: 'DrgName',
    },
    {
      title: '病种名称',
      key: 'ADrgName',
    },
    {
      title: '最高手术级别',
      key: 'CardOperRate',
    },
    {
      title: 'RW',
      key: 'Rw',
    },
    {
      title: '单病种',
      key: 'SdName',
      description: data?.['Sds']?.[0]?.['SdName'] || '-',
    },
  ];
};

export const HqmsListColumns = (data) => {
  return [
    {
      title: '单病种质量控制',
      key: 'Sds',
      type: 'list',
      isSuspected: false,
    },
    {
      title: '潜在单病种质量控制',
      type: 'list',
      key: 'Sds',
      isSuspected: true,
    },
    {
      title: '术后并发症',
      key: 'OperComplications',
      type: 'list',
    },
    {
      title: '三级手术',
      key: 'HqmsThirdOperDetails',
      type: 'string',
    },
    {
      title: '四级手术',
      type: 'string',
      key: 'HqmsFourthOperDetails',
    },
    // {
    //   title: '潜在四级手术',
    //   key: 'HqmsSuspFourth',
    // },
    {
      title: '是否日间手术',
      key: 'IsDaySurgery',
      type: 'bool',
    },
    {
      title: '微创手术',
      type: 'string',
      key: 'MicroSurgeryOperDetails',
    },
  ];
};
