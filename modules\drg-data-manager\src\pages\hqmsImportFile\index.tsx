import React, { useState } from 'react';
import { useModel } from 'umi';
import './index.less';

import UploadArea from './components/UploadArea';
import UploadRecordTab from './components/UploadRecordTab';
// import MonthlyDataTab from './components/MonthlyDataTab'; // 暂时不使用月度数据Tab
import ResultDrawer from './components/ResultDrawer';

/**
 * HQMS文件导入页面
 * 提供文件上传、上传记录查看功能
 */
const HqmsImportFile: React.FC = () => {
  // 从全局状态获取医院代码和字典数据
  const {
    globalState: { hospCodes, dictData },
  } = useModel('@@qiankunStateFromMaster');

  // 抽屉状态
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [drawerTitle, setDrawerTitle] = useState<string>('');
  const [currentMasterId, setCurrentMasterId] = useState<string>('');

  // 处理文件上传成功
  const handleUploadSuccess = (masterId: string) => {
    setCurrentMasterId(masterId);
    setDrawerTitle('处理进度');
    setDrawerVisible(true);
  };

  // 处理查看记录详情
  const handleViewDetails = (record: any) => {
    setCurrentMasterId(record.MasterId);
    setDrawerTitle(record.FileName || '上传详情');
    setDrawerVisible(true);
  };

  return (
    <div className="hqms-import-container">
      {/* 上传区域 */}
      <UploadArea
        hospCodes={hospCodes}
        dictData={dictData}
        onUploadSuccess={handleUploadSuccess}
      />

      {/* 上传记录 */}
      <UploadRecordTab onViewDetails={handleViewDetails} dictData={dictData} />

      {/* 结果抽屉 */}
      <ResultDrawer
        visible={drawerVisible}
        dictData={dictData}
        title={drawerTitle}
        masterId={currentMasterId}
        onClose={() => setDrawerVisible(false)}
      />
    </div>
  );
};

export default HqmsImportFile;
