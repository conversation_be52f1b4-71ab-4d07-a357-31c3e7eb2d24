const TotalStatsColumns = [
  // {
  //   contentData: 'Cm',
  //   title: '总权重',
  //   clickable: true,
  //   footerYoy: true,
  // },
  // {
  //   contentData: 'DrgGrpCntRatio',
  //   title: '病种覆盖率',
  //   clickable: true,
  //   footerYoy: true,
  //   dataType: 'percent',
  // },
  {
    contentData: 'AvgInPeriod',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgTotalFee',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Profit',
    title: '盈亏',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'AvgProfit',
    title: '次均盈亏',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'StdInsurPayment',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'HighPatCnt',
    title: '高倍率人次',
    detailsUrl: 'FundSupervise/LatestDrgSettleStats/SettleDetails',
    type: 'chs',
    args: { AbnFeeType: '3' },
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'LowPatCnt',
    title: '低倍率人次',
    detailsUrl: 'FundSupervise/LatestDrgSettleStats/SettleDetails',
    type: 'chs',
    args: { AbnFeeType: '4' },
    clickable: true,
    footerYoy: true,
  },
];

export const GrpTotalStatsColumns = [
  {
    contentData: 'PatCnt',
    clickable: true,
    footerYoy: true,
  },
  {
    contentData: 'Cw',
    title: 'RW',
    clickable: true,
    footerYoy: true,
  },
  ...TotalStatsColumns,
  {
    contentData: 'ProfitLossPatCnt',
    footerYoy: 'ProfitLossFee',
    type: 'chs',
    detailsUrl: 'FundSupervise/LatestDrgSettleStats/SettleDetails',
    args: { ProfitLoss: true },
    clickable: true,
  },
  {
    contentData: 'ProfitGainPatCnt',
    footerYoy: 'ProfitGainFee',
    type: 'chs',
    detailsUrl: 'FundSupervise/LatestDrgSettleStats/SettleDetails',
    args: { ProfitGain: true },
    clickable: true,
  },
];
