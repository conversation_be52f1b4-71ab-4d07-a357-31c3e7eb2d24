import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _ from 'lodash';
import { ProDescriptions, ProList } from '@ant-design/pro-components';
import {
  BaseInfoColumns,
  IcdeColumns,
  OperColumns,
  DrgListColumns,
  HqmsListColumns,
} from './columns';
import { UniTable } from '@uni/components/src';
import {
  Card,
  Collapse,
  Spin,
  Row,
  Col,
  Badge,
  Tag,
  Space,
  Alert,
  List,
  Tabs,
} from 'antd';
import UniEcharts from '@uni/components/src/echarts';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { useRequest } from 'umi';
import qs from 'qs';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useModel } from '@@/plugin-model/useModel';
import { BulbTwoTone } from '@ant-design/icons';
import { handleChsLabel } from '@uni/utils/src/cwUtils';

const { Panel } = Collapse;

export interface ICardInfoProps {}

const DrgHqmsCardInfo = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  const [currentHisId, setCurrentHisId] = useState('');
  const [urlParam, setUrlParam] = useState<any>({});
  const [cardInfoData, setCardInfoData] = useState<any>({});
  const [drgsMetricsData, setDrgsMetricsData] = useState<any>({});
  const [hqmsMetricsData, setHqmsMetricsData] = useState<any>({});

  const [flag, setFlag] = useState(false);

  // 查询data
  const { run: getDataSourceReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetMetricFeatures',
        {
          method: 'POST',
          data: {},
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          const {
            EnableCustomDrg,
            EnableDrgsMetrics,
            EnableGradeMetrics,
            EnableHqmsDrg,
            EnableHqmsMetrics,
          } = response?.data || {};
          setFlag(EnableHqmsDrg);
        }
      },
    },
  );

  const { loading: cardInfoLoading, run: getChsCardInfo } = useRequest(
    (data) =>
      uniCommonService(`Api/Hqms/HqmsCardBundle/Get`, {
        method: 'POST',
        requestType: 'json',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setCurrentHisId(urlParam?.hisId);
          setCardInfoData(res.data.Card);
          setDrgsMetricsData(res.data.DrgsMetrics);
          setHqmsMetricsData(res.data.HqmsMetrics);
        }
        return null;
      },
    },
  );

  // location
  useEffect(() => {
    if (isEmptyValues(location?.search)) {
      return;
    }
    setUrlParam(
      qs.parse(location.search, {
        ignoreQueryPrefix: true,
      }),
    );
  }, [location?.search]);

  useEffect(() => {
    if (urlParam.hisId && urlParam?.hisId !== currentHisId) {
      getChsCardInfo({
        hisId: decodeURIComponent(urlParam.hisId), // 100477752 100476387
      });
    }
  }, [urlParam?.hisId]);

  // props
  useEffect(() => {
    if (props?.hisId) {
      getDataSourceReq();
      getChsCardInfo({
        // hisId: "100477752",
        hisId: props?.hisId,
      });
    }
  }, [props?.hisId]);

  const RenderResultList = (columns, data, other: any = {}) => {
    let listData = [];
    _.map(columns, (col) => {
      let str = data[col.key];
      // 目前columns.type仅在国考结果中使用
      if (col?.type === 'bool') {
        str = str === false ? '否' : str === true ? '是' : str;
      }

      if (col?.type === 'string') {
        str = str?.[0];
        str = str ? `${str?.['OperCode']} ${str?.['OperName']}` : str;
      }

      if (col?.type === 'list') {
        // 未过滤
        if (!str?.length) str = null;

        str = _.filter(str, (des) => des?.['IsSuspected'] === col?.isSuspected);

        // 过滤后
        if (str?.length)
          str = _.map(str, (des) => {
            let opers = other?.Opers,
              icdes = other?.Icdes;
            return (
              <>
                <div style={{ marginBottom: '0.5rem' }}>
                  {des?.SdName || '-'}
                </div>

                {des?.RelatedIcdes && des?.RelatedIcdes?.length !== 0 && (
                  <h5 className="font-primary">
                    相关诊断：
                    {_.map(des?.RelatedIcdes, (code) => (
                      <Tag color="processing">
                        {code}{' '}
                        {icdes.find((i) => i.IcdeCode === code)?.IcdeName}
                      </Tag>
                    ))}
                  </h5>
                )}

                {des?.RelatedOpers && des?.RelatedOpers?.length !== 0 && (
                  <h5 className="font-primary">
                    相关手术：
                    {_.map(des?.RelatedOpers, (code) => (
                      <Tag color="processing">
                        {code}{' '}
                        {opers.find((i) => i.OperCode === code)?.OperName}
                      </Tag>
                    ))}
                  </h5>
                )}
              </>
            );
          });
        else str = '-';
      }

      listData.push({ description: str || '-', ...col });
    });
    return (
      <List
        className="hqms-card-list"
        itemLayout="horizontal"
        dataSource={listData}
        renderItem={(item: any, index) => (
          <List.Item>
            <List.Item.Meta
              avatar={<Badge color="blue" />}
              title={<>{item?.title}</>}
              description={
                <>
                  <>{item?.description}</>
                </>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <>
      <div className="card-info-container">
        <Row gutter={[32, 16]}>
          <Col span={15} className="border-right">
            <>
              <h3>
                <b>基本信息</b>
              </h3>
              <Row gutter={[10, 10]}>
                <Col span={24}>
                  <ProDescriptions
                    className="base-info-descriptions"
                    // size="small"
                    column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 4 }}
                    dataSource={{
                      ...cardInfoData,
                      ...drgsMetricsData,
                      // ...cardInfoData?.CardFlat,
                      // ...metricSettlesData,
                    }}
                    columns={BaseInfoColumns(dictData)}
                  />
                </Col>
                {/* <Divider style={{margin:0}}/> */}
                <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                  <Collapse ghost defaultActiveKey={['1', '2']}>
                    <Panel
                      header={<b className="font-primary">诊断信息</b>}
                      key="1"
                      showArrow={false}
                    >
                      <UniTable
                        loading={cardInfoLoading}
                        id={'cardInfoData-icdeResult-icdeDscgs-table'}
                        rowKey={'id'}
                        scroll={{
                          x: 'max-content',
                          y: '200px',
                        }}
                        dictionaryData={dictData}
                        columns={IcdeColumns(dictData)}
                        dataSource={_.orderBy(cardInfoData?.Icdes, 'IcdeSort')}
                      />
                    </Panel>
                    <Panel
                      header={<b className="font-primary">手术信息</b>}
                      key="2"
                      showArrow={false}
                    >
                      <UniTable
                        loading={cardInfoLoading}
                        id={'cardInfoData-icdeResult-icdeDscgs-table'}
                        rowKey={'id'}
                        scroll={{
                          x: 'max-content',
                          y: '200px',
                        }}
                        dictionaryData={dictData}
                        columns={OperColumns}
                        dataSource={_.orderBy(cardInfoData?.Opers, 'OperSort')}
                      />
                    </Panel>
                  </Collapse>
                </Col>
              </Row>
            </>
          </Col>
          <Col span={9}>
            <>
              <h3>
                <b>病案分析</b>
              </h3>
              <Tabs
                size="small"
                defaultActiveKey="1"
                items={[
                  {
                    key: '1',
                    label: 'DRG计算结果',
                    children: RenderResultList(
                      DrgListColumns(drgsMetricsData),
                      drgsMetricsData,
                    ),
                  },
                  {
                    key: '2',
                    label: '国考指标分析',
                    children: RenderResultList(
                      HqmsListColumns(flag),
                      hqmsMetricsData,
                      cardInfoData,
                    ),
                  },
                ]}
              />
            </>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default DrgHqmsCardInfo;
