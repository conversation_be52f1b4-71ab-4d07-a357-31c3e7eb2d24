import { <PERSON><PERSON>, <PERSON>, Divider, Space, Tooltip } from 'antd';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useRef, useState } from 'react';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { useDeepCompareEffect } from 'ahooks';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { RedoOutlined } from '@ant-design/icons';

const MaintenanceRecordDataCnt = () => {
  const {
    globalState: { searchParams },
  } = useModel('@@qiankunStateFromMaster');

  const { dateRange, hospCodes } = searchParams;

  const ref = useRef<any>();

  const [dataSource, setDataSource] = useState([]);

  const [tableParams, setTableParams] = useState(undefined);

  const [dataSourceColumns, setDataSourceColumns] = useState([]);

  useDeepCompareEffect(() => {
    let params: any = {
      Sdate: dateRange?.at(0),
      Edate: dateRange?.at(1),
      HospCode: hospCodes || [],
    };
    setTableParams(params);
    if (dateRange && dateRange.length) {
      getDataSourceColumnsReq();
      getDataSourceReq(params);
    }
  }, [dateRange, hospCodes]);

  // 查询data
  const { loading: dataSourceLoading, run: getDataSourceReq } = useRequest(
    (params) => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetCalculateResults',
        {
          method: 'POST',
          data: params,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        if (response.code === 0 && response?.statusCode === 200) {
          setDataSource(response?.data);
        } else {
          setDataSource([]);
        }
      },
    },
  );

  // Columns
  const {
    data: tableColumns,
    mutate: mutateDescriptionColumns,
    run: getDataSourceColumnsReq,
  } = useRequest(
    () => {
      return uniCommonService(
        'Api/MedQuality/DataManagement/GetCalculateResults',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          let list = tableColumnBaseProcessor([], response?.data?.Columns);
          list = list.map((i) => {
            if (!['SubFeaturesDisplay'].includes(i?.name)) {
              i['width'] = 140;
            }
            if (i?.name === 'ErrMsg') {
              i['ellipsis'] = true;
              i['width'] = 220;
            }
            if (i.name === 'SubFeaturesDisplay') {
              i['ellipsis'] = true;
            }
            return {
              ...i,
            };
          });
          setDataSourceColumns(list);
        } else {
          setDataSourceColumns([]);
        }
      },
    },
  );

  return (
    <Card
      title={'数据分组记录'}
      extra={
        <Space>
          <Divider type="vertical" />
          <TableColumnEditButton
            columnInterfaceUrl="Api/MedQuality/DataManagement/GetCalculateResults"
            onTableRowSaveSuccess={(newColumns) => {
              mutateDescriptionColumns(
                tableColumnBaseProcessor(
                  [],
                  newColumns?.map((d) => ({
                    ...d,
                    valueType: d?.columnType === 'Boolean' ? 'boolean' : '',
                  })),
                ),
              );
            }}
          />
          <Tooltip title="刷新">
            <Button
              type={'text'}
              shape={'default'}
              icon={
                <RedoOutlined
                  onPointerEnterCapture={() => {}}
                  onPointerLeaveCapture={() => {}}
                />
              }
              onClick={() => {
                getDataSourceReq(tableParams);
              }}
            ></Button>
          </Tooltip>
        </Space>
      }
    >
      <UniTable
        actionRef={ref}
        forceColumnsUpdate
        id={`user-data-cnt-key-table`}
        className={'user-data-cnt-key-table'}
        rowKey={'MonthlyDataId'}
        scroll={{ y: 540 }}
        bordered={true}
        loading={dataSourceLoading}
        columns={dataSourceColumns}
        dataSource={dataSource}
        clickable={false}
        toolBarRender={null}
      />
    </Card>
  );
};

export default MaintenanceRecordDataCnt;
