import flattenDeep from 'lodash/flattenDeep';
import { contentData } from '@/pages/dmr/fields/base';
import FieldAdapter from '@/pages/dmr/field-adapter';
import { TimeRangeItem } from '@uni/grid/src/components/range-stats';
import { isEmptyValues } from '@uni/utils/src/utils';
import { filterIdAddAndAllCellEmptyRow } from '@/pages/dmr/processors/processors';
const { chain } = require('mathjs');

export const newBornAgeRequestParamProcessor = (data) => {
  const ageAio = document.querySelector('input[id*=PatAgeYMD]');
  if (isEmptyValues(ageAio)) {
    if (isEmptyValues(data?.['NwbAge'])) {
      // 不存在 新生儿天数  就置空
      data['NwbAgeDay'] = null;
      data['NwbAgeMonth'] = null;
    } else {
      let age = data?.['NwbAge'];
      let month = age >= 30 ? Math.floor(age / 30) : 0;
      let day = chain(age)
        .subtract(month * 30)
        .done();

      data['NwbAgeMonth'] = month;
      data['NwbAgeDay'] = day;
    }
  } else {
    if (!isEmptyValues(data?.['NwbAge'])) {
      data['NwbAgeDay'] = data?.['NwbAge'] % 30;
    }
  }
};

export const timeRangeStatsTransformRequestParamProcessor = (
  hourOnlyItems: any[],
  items: any[],
  data: any,
) => {
  hourOnlyItems?.forEach(
    (hourOnlyItem: TimeRangeItem, hourOnlyIndex: number) => {
      let hourOnlyItemValue = data?.[hourOnlyItem?.formKeys?.[0]];
      if (!isEmptyValues(hourOnlyItemValue)) {
        let day =
          hourOnlyItemValue >= 24 ? Math.floor(hourOnlyItemValue / 24) : 0;
        let hour = Math.floor(Math.max(hourOnlyItemValue - day * 24, 0));
        let minute = Math.ceil(
          chain(hourOnlyItemValue)
            .subtract(day * 24)
            .subtract(hour)
            .multiply(60)
            .done(),
        );

        console.log('timeRangeStatsTransform', day, hour, minute);

        items?.[hourOnlyIndex]?.formKeys?.forEach(
          (formKey: string, formKeyIndex: number) => {
            let inputKey = items?.[hourOnlyIndex]?.inputKeys?.[formKeyIndex];

            switch (inputKey) {
              case 'day':
                data[formKey] = day;
                break;
              case 'hour':
                data[formKey] = hour;
                break;
              case 'minute':
                // 理论上仅可能为整数
                data[formKey] = 0;
                break;
              default:
                break;
            }
          },
        );
      } else {
        // 如果是空 就要 天时分都换掉
        items?.[hourOnlyIndex]?.formKeys?.forEach((formKey) => {
          data[formKey] = undefined;
        });
      }
    },
  );
};

export const timeRangeStatsTransformResponseProcessor = (
  hourOnlyItems: any[],
  items: any[],
  formFieldValue: any,
) => {
  items?.forEach((rangeItem: TimeRangeItem, rangeIndex: number) => {
    let hourOnlyFormKeyValue = 0;
    // 一个formItem
    rangeItem?.formKeys?.forEach((formKey: string, formKeyIndex: number) => {
      let inputKey = rangeItem?.inputKeys[formKeyIndex];
      switch (inputKey) {
        case 'day':
          hourOnlyFormKeyValue +=
            parseInt(formFieldValue?.[formKey] ?? '0') * 24;
          break;
        case 'hour':
          hourOnlyFormKeyValue += parseInt(formFieldValue?.[formKey] ?? '0');
          break;
        case 'minute':
          // hourOnlyFormKeyValue += Math.round(((parseInt(formFieldValue?.[formKey] ?? "0") / 60) + Number.EPSILON) * 10) / 10
          hourOnlyFormKeyValue += 0;
          break;
        default:
          break;
      }
    });

    // 对应的小时 数据改掉
    if (hourOnlyItems?.[rangeIndex]?.formKeys?.[0]) {
      formFieldValue[hourOnlyItems?.[rangeIndex]?.formKeys?.[0]] =
        hourOnlyFormKeyValue;
    }
  });
  return formFieldValue;
};

export const addressPlaceProcessor = (data, formFieldValues, modelData) => {
  let flattenContentData = flattenDeep(contentData?.slice());
  ['NativePlace', 'BirthPlace', 'CurAddress', 'RegistAddress']?.forEach(
    (key) => {
      let contentItem = flattenContentData?.find((item) =>
        FieldAdapter.provinceSelectorDataKeyChecker(item, key),
      );

      // 理论上 地址为输入框的时候 上面的contentItem 对象也不存在
      let addressPlaceInputExists = fullAddressFormItemExists(
        flattenContentData,
        key,
      );

      // contentItem 存在并且 noUpdateFormKey 不能为true
      if (contentItem && contentItem?.data?.noUpdateFormKey !== true) {
        let formKeyValue = '';
        contentItem?.data?.props?.itemFormKeys?.forEach((itemKey, index) => {
          if (formFieldValues?.[itemKey]) {
            if (index === contentItem?.data?.props?.itemFormKeys?.length - 1) {
              formKeyValue += formFieldValues?.[itemKey] || '';
            } else {
              let keySuffix = itemKey.slice(-4);
              let labelValue = modelData[keySuffix]?.find(
                (item) => item.Code === formFieldValues?.[itemKey],
              )?.Name;
              if (labelValue) {
                formKeyValue += labelValue || '';
              }
            }
          }
        });

        if (formKeyValue) {
          data[key] = formKeyValue;
        }
      }
    },
  );
};

export const dmrThemeProcessor = (dmrTheme: any) => {
  Object.keys(dmrTheme)?.forEach((key) => {
    document
      ?.getElementById('dmr-form-container')
      ?.style?.setProperty(key, dmrTheme[key]);
  });
};

export const fullAddressFormItemExists = (flattenContentData: any, key) => {
  // 判定是否存在 输入框的 ['NativePlace', 'BirthPlace', 'CurAddress', 'RegistAddress'] 如果存在也不会更新
  return (
    flattenContentData?.find(
      (item) => item?.data?.key === key && item?.data?.component === 'Input',
    ) !== undefined
  );
};

export const isMainInVisibleSetFirstLineIsMainTrue = (formFieldsValue: any) => {
  let icdeIsMainColumn = document.querySelector(
    '#diagnosisTable table thead th[id=th-IsMain]',
  );
  let operIsMainColumn = document.querySelector(
    '#operationTable table thead th[id=th-IsMain]',
  );

  if (isEmptyValues(icdeIsMainColumn)) {
    formFieldsValue['diagnosis-table']
      ?.filter((item) => filterIdAddAndAllCellEmptyRow(item))
      ?.map((item, index) => {
        item['IsMain'] = index === 0;
      });
  }

  if (isEmptyValues(operIsMainColumn)) {
    formFieldsValue['operation-table']
      ?.filter((item) => filterIdAddAndAllCellEmptyRow(item))
      ?.map((item, index) => {
        item['IsMain'] = index === 0;
      });
  }
};
