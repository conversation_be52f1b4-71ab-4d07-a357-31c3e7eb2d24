import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _ from 'lodash';
import { ProDescriptions, ProList } from '@ant-design/pro-components';
import { BaseInfoColumns, FeeTypesColumns } from './columns';
import { UniTable } from '@uni/components/src';
import { ChsDrgResultColumns, ChsDipResultColumns } from './columns';
import {
  Card,
  Collapse,
  Spin,
  Row,
  Col,
  Badge,
  Tag,
  Space,
  Alert,
  List,
  Divider,
} from 'antd';
import UniEcharts from '@uni/components/src/echarts';
import { FeeTypesBarOption } from './chart.opts';
import { v4 as uuidv4 } from 'uuid';
import './index.less';
import { useRequest } from 'umi';
import qs from 'qs';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import GroupDetail from './components/groupDetail';
import FeeTypes from './components/feeTypes';
import { useModel } from '@@/plugin-model/useModel';
import { BulbTwoTone } from '@ant-design/icons';
import { handleChsLabel } from '@uni/utils/src/cwUtils';

const { Panel } = Collapse;

export interface ICardInfoProps {}

const CardInfo = (props) => {
  const {
    globalState: { dictData },
  } = useModel('@@qiankunStateFromMaster');
  const [currentHisId, setCurrentHisId] = useState('');
  const [urlParam, setUrlParam] = useState<any>({});
  const [cardInfoData, setCardInfoData] = useState<any>({});
  const [feeTypesData, setFeeTypesData] = useState<any>([]);
  const [metricSettlesData, setMetricSettlesData] = useState<any>({});
  const [metricAdjustDrgsData, setMetricAdjustDrgsData] = useState<any>([]);

  const { loading: cardInfoLoading, run: getChsCardInfo } = useRequest(
    (data) =>
      uniCommonService(`Api/FundSupervise/LatestCardBundle/Get`, {
        method: 'POST',
        requestType: 'json',
        data,
      }),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          setCurrentHisId(urlParam?.hisId);
          setCardInfoData(res.data);
        }
        return null;
      },
    },
  );

  // location
  useEffect(() => {
    if (isEmptyValues(location?.search)) {
      return;
    }
    setUrlParam(
      qs.parse(location.search, {
        ignoreQueryPrefix: true,
      }),
    );
  }, [location?.search]);

  useEffect(() => {
    if (urlParam.hisId && urlParam?.hisId !== currentHisId) {
      getChsCardInfo({ hisId: decodeURIComponent(urlParam.hisId) });
    }
  }, [urlParam?.hisId]);

  // props
  useEffect(() => {
    if (props?.hisId) {
      getChsCardInfo({ hisId: props?.hisId });
    }
  }, [props?.hisId]);

  useEffect(() => {
    if ((urlParam?.type || props?.type) === 'dip') {
      setMetricSettlesData(
        cardInfoData?.MetricDipSettles?.length
          ? cardInfoData.MetricDipSettles[0]
          : {},
      );
    } else {
      setMetricSettlesData(
        cardInfoData?.MetricDrgSettles?.length
          ? cardInfoData.MetricDrgSettles[0]
          : {},
      );
    }
  }, [urlParam?.type, props?.type, cardInfoData]);

  useEffect(() => {
    setMetricAdjustDrgsData(cardInfoData?.MetricAdjustDrgs);
  }, [cardInfoData]);

  useEffect(() => {
    setFeeTypesData(
      _.orderBy(cardInfoData?.FeeTypes, [(d) => d?.TotalFee || ''], 'desc'),
    );
  }, [dictData?.Insur?.MedChrgitm, cardInfoData]);

  const handleChsArr = (array) => {
    let cwTypes = {};
    try {
      cwTypes = JSON.parse(
        sessionStorage?.getItem('configurationInfo') ?? '{}',
      )?.chsDefs;
    } catch (e) {
      console.warn('handleChsArr JSON error', e);
    }
    return handleChsLabel(array, 'key', 'title', cwTypes);
  };

  return (
    <>
      <div className="card-info-container">
        <Row gutter={[16, 16]}>
          <Col span={7}>
            <Badge.Ribbon
              text={
                (urlParam?.type || props?.type) === 'dip'
                  ? cardInfoData?.MetricDipSettles?.[0]?.Applicable
                    ? 'DIP结算'
                    : '非DIP结算'
                  : cardInfoData?.MetricDrgSettles?.[0]?.Applicable
                  ? 'DRG结算'
                  : '非DRG结算'
              }
              color="pink"
              placement="end"
            >
              <Card
                title={<>基本信息</>}
                loading={cardInfoLoading}
                size="small"
              >
                <Row gutter={[10, 10]}>
                  <Col span={24}>
                    <ProDescriptions
                      className="base-info-descriptions"
                      // size="small"
                      column={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 2 }}
                      dataSource={{
                        ...cardInfoData?.CardFlat,
                        ...metricSettlesData,
                      }}
                      columns={BaseInfoColumns(dictData)}
                    />
                  </Col>
                  {/* <Divider style={{margin:0}}/> */}
                  <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                    <Collapse
                      ghost
                      defaultActiveKey={
                        cardInfoData?.IcdeResult?.IcdeDscgs?.length ? ['1'] : []
                      }
                    >
                      <Panel header={'诊断信息'} key="1">
                        <List
                          size="small"
                          itemLayout="horizontal"
                          dataSource={cardInfoData?.IcdeResult?.IcdeDscgs || []}
                          renderItem={(item: any) => (
                            <List.Item>
                              <span>
                                {item?.IsMain ? (
                                  <Tag
                                    color="error"
                                    style={{ borderColor: 'transparent' }}
                                  >
                                    主诊
                                  </Tag>
                                ) : null}
                                {item?.IcdeCode || ''}
                                &nbsp;&nbsp;
                                {item?.IcdeName || ''}
                              </span>
                            </List.Item>
                          )}
                        />
                      </Panel>
                    </Collapse>
                  </Col>
                  <Col span={24}>
                    <Collapse
                      ghost
                      defaultActiveKey={
                        cardInfoData?.Opers?.length ? ['1'] : []
                      }
                    >
                      <Panel header={'手术信息'} key="1">
                        <List
                          size="small"
                          itemLayout="horizontal"
                          dataSource={cardInfoData?.Opers || []}
                          renderItem={(item: any) => (
                            <List.Item>
                              <span>
                                {item?.IsMain ? (
                                  <Tag
                                    color="error"
                                    style={{ borderColor: 'transparent' }}
                                  >
                                    主手术
                                  </Tag>
                                ) : (
                                  <></>
                                )}
                                {item?.OperCode || ''}
                                &nbsp;&nbsp;
                                {item?.OperName || ''}
                              </span>
                            </List.Item>
                          )}
                        />
                      </Panel>
                    </Collapse>
                  </Col>
                </Row>
              </Card>
            </Badge.Ribbon>
          </Col>
          <Col span={17}>
            <Row gutter={[16, 16]}>
              {/* 支付审核结果 */}
              <Col span={24}>
                <Alert
                  type={
                    cardInfoData?.SettleCheckCardResult?.WarningLevel?.toLowerCase() ===
                    'red'
                      ? 'error'
                      : cardInfoData?.SettleCheckCardResult?.WarningLevel?.toLowerCase() ===
                        'yellow'
                      ? 'warning'
                      : cardInfoData?.SettleCheckCardResult?.WarningLevel?.toLowerCase() ===
                        'orange'
                      ? 'orange'
                      : 'success'
                  }
                  message={
                    <>
                      <b className="mr-1">支付审核结果</b>
                      <Space size={[8, 8]} wrap>
                        {cardInfoData?.SettleCheckReviewResults?.map((item) => {
                          return (
                            <Badge
                              text={item?.ErrMsg}
                              color={
                                item?.WarningLevel?.toLowerCase() || 'green'
                              }
                            />
                          );
                        })}
                      </Space>
                    </>
                  }
                />
              </Col>
              {/* 当前入组 */}
              <Col span={24}>
                <Spin spinning={cardInfoLoading || false}>
                  <GroupDetail
                    type={urlParam?.type}
                    metricSettlesData={metricSettlesData}
                  />
                </Spin>
              </Col>
              {/* 调整组 */}
              <Col span={24}>
                <Spin spinning={cardInfoLoading || false}>
                  <Card size="small" title={''}>
                    <Collapse
                      ghost
                      defaultActiveKey={
                        metricAdjustDrgsData?.length ? ['1'] : []
                      }
                    >
                      <Panel header={'可调整组'} key="1">
                        <div>
                          <b>
                            {metricAdjustDrgsData?.[0]?.ChsDrgCode || ''}
                            &nbsp;&nbsp;
                            {metricAdjustDrgsData?.[0]?.ChsDrgName || ''}
                          </b>
                          <ProDescriptions
                            size="small"
                            column={{
                              xs: 1,
                              sm: 1,
                              md: 2,
                              lg: 4,
                              xl: 4,
                              xxl: 6,
                            }}
                            dataSource={metricAdjustDrgsData?.[0]}
                            columns={
                              props?.type === 'dip'
                                ? handleChsArr(
                                    ChsDipResultColumns(dictData?.Dmr || {}),
                                  )
                                : handleChsArr(
                                    ChsDrgResultColumns(dictData?.Dmr || {}),
                                  )
                            }
                          />
                          <span
                            style={{
                              paddingLeft: '7px',
                              color: '#555',
                              fontStyle: 'italic',
                              borderLeft: '2px solid #eb5757',
                            }}
                          >
                            {metricAdjustDrgsData?.[0]?.ShuffleMessage}
                          </span>
                        </div>
                      </Panel>
                    </Collapse>
                  </Card>
                </Spin>
              </Col>
              {/* 分类费用 */}
              <Col span={24}>
                <FeeTypes
                  hisId={props?.hisId}
                  chsDrgCode={metricSettlesData?.ChsDrgCode}
                  loading={cardInfoLoading}
                  data={feeTypesData}
                  columns={FeeTypesColumns}
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default CardInfo;
