import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { UniTable } from '@uni/components/src';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { ColumnItem, RespVO } from '@uni/commons/src/interfaces';
import {
  ReportMasterItem,
  ReportBaseInfo,
  ReportDetailItem,
  ReportItem,
  TableBaseProps,
  ValidationItem,
} from '@/interfaces';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ReportDataSize, ReportEventConstant } from '@/constants';
import {
  Card,
  Button,
  Col,
  message,
  notification,
  Row,
  TableProps,
  Space,
  Form,
  Divider,
  Tooltip,
} from 'antd';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import {
  ARCHIVE,
  columns,
  DOWNLOAD,
  EDIT,
  EXPORT,
  IMPORT,
  LOAD,
  LOCK,
  REFRESH,
  ROW_ADD,
  ROW_EDIT,
  SAVE,
  UNLOCK,
  EXPORT_DOWNLOAD,
} from '@/tables/constants';
import {
  archiveReport,
  downloadTemplateReport,
  importReport,
  loadReport,
  reloadReport,
  syncValidateReport,
} from '@/tables/network';
import {
  downloadReportByBlobId,
  masterItemExtraConfig,
  reportBackgroundExport,
  reportTableGroupNameHeaderProcessor,
  showNotification,
  transformReportColumnsWithDataTypeIntOrderable,
} from '@/utils';
import { debounce } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import isNil from 'lodash/isNil';
import ReportSingleLine from '@/components/single-line';
import dayjs from 'dayjs';
import reportBase from '@/components/left-menu/report-base';
import { ValidationResult } from '@/tables/validation';
import ReportBase from '@/components/left-menu/report-base';
import isEmpty from 'lodash/isEmpty';
import { exportExcel } from '@uni/utils/src/excel-export';
import { exportExcelDictionaryModuleProcessor } from '@uni/components/src/table/processor/data/export';
import cloneDeep from 'lodash/cloneDeep';
import { useModel } from '@@/plugin-model/useModel';
import { FallOutlined, PushpinOutlined } from '@ant-design/icons';
import { virtualConfig } from '@uni/components/src/table/virtual';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';
import { useAntdResizableHeader } from '@uni/components/src/table/resizable-column/header';
import { UseDispostionEnum, downloadFile } from '@uni/utils/src/download';
import { externalPaginationProcessor } from '@uni/components/src/table/pagination';
import { isEmptyValues } from '@uni/utils/src/utils';
import PivotTransformService, { hasPivotColumn } from '@/tables/pivot';
import { DateTypeToValueType } from '@uni/components/src/table/processor/column/value-type';

const pivotServiceInstance = new PivotTransformService();

interface StatsReportPersistTableProps extends TableBaseProps {}

const StatsReportPersistTable = (props: StatsReportPersistTableProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const [form] = Form.useForm();

  const actionRef = useRef<any>();

  const [reportBaseInfo, setReportBaseInfo] =
    useState<ReportBaseInfo>(undefined);

  const [reportLoading, setReportLoading] = useState(false);

  const [reportTableDataSource, setReportTableDataSource] = useState([]);
  const [reportTableColumns, setReportTableColumns] = useState([]);

  const [reportTableExtraColumns, setReportTableExtraColumns] = useState([]);

  const [reportItem, setReportItem] = useState<ReportItem>(undefined);
  const [masterItem, setMasterItem] = useState<ReportMasterItem>(undefined);

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const [reportArchiveBaseInfo, setReportArchiveBaseInfo] =
    useState<ReportBaseInfo>(undefined);
  const [validationFailureData, setValidationFailureData] = useState([]);

  const [reportArchiveLoading, setReportArchiveLoading] = useState(false);

  const [reportValidateFailureOpen, setReportValidateFailureOpen] =
    useState(false);

  const [reportImportLoading, setReportImportLoading] = useState(false);

  const tableEditingData = useRef({});

  const { components, resizableColumns, tableWidth, resetColumns } =
    useAntdResizableHeader({
      columns: React.useMemo(() => {
        return reportTableColumns;
      }, [reportTableColumns]),
    });

  /**
   * 前端分页 start
   */
  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 30,
    pageSizeOptions: ['10', '20', '30', '50'],
    hideOnSinglePage: false,
    ...externalPaginationProcessor(true),
  });

  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagination) => {
    setFrontPagination({
      ...frontPagination,
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  /**
   * 前端分页 end
   */

  useEffect(() => {
    // 卸载的时候停止刷新
    return () => {
      // 取消轮询
      stopPolling();
      Emitter.off(ReportEventConstant.REPORT_ITEM_CLICK);
    };
  }, []);

  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_ITEM_CLICK, async (data) => {
      if (
        data?.masterItem === undefined ||
        data?.reportItem === undefined ||
        (data?.masterItem?.Id === masterItem?.Id &&
          data?.reportItem?.ReportBaseId === reportItem?.ReportBaseId)
      ) {
        return;
      }

      // 重置 validation 数据
      setValidationFailureData([]);

      // 停止轮询
      stopPolling();
      // report item & master item
      setReportItem(data?.reportItem);

      resetState();
      // 解析ExtraConfig
      let extraConfigs = masterItemExtraConfig(masterItem?.ExtraConfig);
      if (!isEmptyValues(extraConfigs?.extraColumns)) {
        setReportTableExtraColumns(extraConfigs?.extraColumns ?? []);
      }

      setMasterItem(data?.masterItem);

      if (data?.reportItem?.ReportStatus === '999') {
        message.error(data?.reportItem?.ReportStatusName);
      } else {
        setReportLoading(true);

        // report base info
        let reportInfo = await reportBaseInfoReq(
          data?.reportItem?.ReportSettingMasterId,
          data?.reportItem?.ReportBaseId,
        );

        let reportColumns = await reportColumnsReq(
          data?.reportItem?.ReportSettingMasterId,
        );
        setReportTableColumns(
          (extraConfigs?.extraColumns ?? []).concat(
            reportColumns?.map((item, index) => {
              let baseId = uuidv4();
              return {
                ...item,
                fieldProps: {
                  className: 'editable-form-item',
                  controls: false,
                  keyboard: false,
                },
                valueType:
                  item?.['valueType'] ||
                  DateTypeToValueType[item?.['dataType'] || 'Text'],
              };
            }),
          ),
        );

        reportDataReq(
          reportInfo?.ReportSettingMasterId,
          reportInfo?.ReportBaseId,
          false,
        );
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_ITEM_CLICK);
    };
  }, [reportItem, masterItem]);

  useEffect(() => {
    Emitter.on(EventConstant.EDITABLE_SAVE_SHORTCUT, () => {
      onTableDataSourceSave();
    });

    Emitter.on(ReportEventConstant.STATS_PERSIST_EDIT, () => {
      // 都能改
      setEditableColumnKeys(reportTableDataSource?.map((item) => item?.rowId));
    });

    Emitter.on(EventConstant.EDITABLE_DELETE_SHORTCUT, (tableDataSource) => {
      setReportTableDataSource(tableDataSource);
    });

    Emitter.on(ReportEventConstant.REPORT_EXPORT, () => {
      message.success('导出中');

      // 后端导出
      if (masterItem?.EnableBackgroundExport) {
        reportBackgroundExport(
          reportBaseInfo?.Title,
          masterItem?.Id,
          reportBaseInfo?.ReportBaseId?.toString(),
          undefined,
        );
        return;
      }

      let exportName = `${reportBaseInfo?.Title}-${dayjs().format(
        'YYYYMMDD_HHmmss',
      )}`;

      const canExportColumns = reportTableColumns?.filter(
        (columnItem) =>
          columnItem.className?.indexOf('exportable') !== -1 &&
          columnItem.valueType !== 'option' &&
          columnItem.dataIndex !== 'operation' &&
          columnItem.dataIndex !== 'directTo',
      );
      if (!isEmpty(canExportColumns)) {
        exportExcel(
          canExportColumns.slice() as any[],
          exportExcelDictionaryModuleProcessor(
            canExportColumns,
            cloneDeep(reportTableDataSource.slice()),
            globalState?.dictData,
          ),
          exportName,
          [],
        );
        message.success('导出成功');
      }
    });

    // 上传
    Emitter.on(
      ReportEventConstant.STATS_PERSIST_IMPORT_DOWNLOAD_TEMPLATE,
      () => {
        // 下载模板
        getTemplateAndDownload();
      },
    );
    Emitter.on(
      ReportEventConstant.STATS_PERSIST_IMPORT_UPLOAD_TEMPLATE,
      (file) => {
        onImportClick(file);
      },
    );

    return () => {
      Emitter.off(EventConstant.EDITABLE_SAVE_SHORTCUT);
      Emitter.off(ReportEventConstant.REPORT_EXPORT);
      Emitter.off(ReportEventConstant.STATS_PERSIST_EDIT);
      Emitter.off(EventConstant.EDITABLE_DELETE_SHORTCUT);
      Emitter.off(ReportEventConstant.STATS_PERSIST_IMPORT_DOWNLOAD_TEMPLATE);
      Emitter.off(ReportEventConstant.STATS_PERSIST_IMPORT_UPLOAD_TEMPLATE);
    };
  }, [
    reportTableDataSource,
    reportBaseInfo,
    masterItem,
    globalState?.dictData,
  ]);

  // 刷新数据 下载数据
  useEffect(() => {
    // 下载
    Emitter.on(
      ReportEventConstant.REPORT_DOWNLOAD,
      debounce(
        () => {
          onDownloadClick(reportBaseInfo, masterItem);
        },
        1000,
        {
          leading: true,
          trailing: false,
        },
      ),
    );

    // 刷新数据
    Emitter.on(ReportEventConstant.REPORT_REFRESH, async () => {
      setReportTableDataSource([]);
      setValidationFailureData([]);
      setFrontPagination({
        current: 1,
        pageSize: 30,
        pageSizeOptions: ['10', '20', '30', '50'],
        hideOnSinglePage: false,
        ...externalPaginationProcessor(true),
      });
      setReportLoading(true);
      reportDataReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
        // false, // reload = false when import 20240311 028cc9ddd2ee8e76cd93456c354087c6b2e8fcb2
        !(
          masterItem?.EnableImportByUdf === true ||
          masterItem?.EnableImportByDefault === true
        ),
      );
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_DOWNLOAD);
      Emitter.off(ReportEventConstant.REPORT_REFRESH);
    };
  }, [reportBaseInfo, masterItem]);

  // 锁定 解锁
  useEffect(() => {
    Emitter.on(ReportEventConstant.REPORT_LOCK, async () => {
      let reportLockResponse = await reportBaseInfoDataLockReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );

      if (
        reportLockResponse?.code === 0 &&
        reportLockResponse?.statusCode === 200
      ) {
        reportBaseInfoReq(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );

        lockUnlockReportChangeColumns(true);
      }
    });

    Emitter.on(ReportEventConstant.REPORT_UNLOCK, async () => {
      let reportUnLockResponse = await reportBaseInfoDataUnlockReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
      );

      if (
        reportUnLockResponse?.code === 0 &&
        reportUnLockResponse?.statusCode === 200
      ) {
        reportBaseInfoReq(
          reportBaseInfo?.ReportSettingMasterId,
          reportBaseInfo?.ReportBaseId,
        );

        lockUnlockReportChangeColumns(false);
      }
    });

    return () => {
      Emitter.off(ReportEventConstant.REPORT_LOCK);
      Emitter.off(ReportEventConstant.REPORT_UNLOCK);
    };
  }, [reportBaseInfo]);

  const resetState = () => {
    //reset
    setReportTableDataSource([]);
    setReportTableColumns([]);
    setValidationFailureData([]);
    setFrontPagination({
      current: 1,
      pageSize: 30,
      pageSizeOptions: ['10', '20', '30', '50'],
      hideOnSinglePage: false,
      ...externalPaginationProcessor(true),
    });
  };
  console.log('masterItem', masterItem);
  const processOperationBtns = () => {
    let btns = [];
    if (reportBaseInfo?.ReportStatus === '2') {
      btns.push(SAVE(reportLoading));
      btns.push(EDIT(reportLoading));

      if (reportBaseInfo?.IsLocked === false) {
        // 锁定
        btns.push(LOCK(reportLoading));
        // 刷新
        btns.push(REFRESH(false, reportLoading));
      }

      if (reportBaseInfo?.IsLocked) {
        btns.push(UNLOCK(reportLoading));
      }

      // btns.push(EXPORT(reportLoading));

      // 归档下载
      // if (masterItem?.EnableArchive) {
      //   btns.push(
      //     DOWNLOAD(
      //       reportTableDataSource?.length === 0 ||
      //         validationFailureData?.length !== 0,
      //       reportArchiveLoading,
      //     ),
      //   );
      // }

      btns.push(<Divider type="vertical" />);
      // 上传
      if (masterItem?.EnableImportByDefault) {
        btns.push(IMPORT(false, false, reportImportLoading));
      }
      if (masterItem?.EnableImportByUdf) {
        btns.push(IMPORT(false, true, reportImportLoading));
      }

      // 归档于导出 合并为  EnableBackgroundExport true => 调用后端Export接口导出 不走ExportQueryDetails
      // 只要导出或归档功能中有一个可用，就显示按钮
      const exportDisabled = reportLoading || !masterItem?.EnableExport;
      const archiveDisabled =
        reportTableDataSource?.length === 0 ||
        validationFailureData?.length !== 0;

      if (masterItem?.EnableExport || masterItem?.EnableArchive) {
        btns.push(
          EXPORT_DOWNLOAD(
            reportLoading || reportArchiveLoading,
            {
              disabled: exportDisabled,
              isBackend: false, // 这个参数是表示要不要走ExportQueryDetails
            },
            {
              disabled: archiveDisabled || !masterItem?.EnableArchive,
              btnText: masterItem?.ArchiveDescription,
              loading: reportArchiveLoading,
            },
          ),
        );
      }
    }

    // 最后添加 错误报告 打开/关闭 按钮
    btns.push(
      <Tooltip title="错误报告">
        <Button
          type="text"
          shape="circle"
          icon={<PushpinOutlined />}
          onClick={() => {
            setReportValidateFailureOpen(!reportValidateFailureOpen);
          }}
        />
      </Tooltip>,
    );

    // setOperationBtns(btns);
    return btns;
  };

  const getTemplateAndDownload = async () => {
    let res = await downloadTemplateReport(
      reportItem?.ReportSettingMasterId,
      reportItem?.ReportBaseId,
      reportBaseInfo?.Title,
    );
    downloadFile(
      `${reportBaseInfo?.Title}.xlsx`,
      res?.response,
      UseDispostionEnum.custom,
    );
  };

  const onImportClick = async (file) => {
    let res = await importReport(
      reportItem?.ReportSettingMasterId,
      reportItem?.ReportBaseId,
      file,
    );
    if (res?.code === 0 && res?.statusCode === 200) {
      message.success('导入成功');
      // 重新加载
      Emitter.emit(ReportEventConstant.REPORT_REFRESH);
    }
  };

  const onDownloadClick = async (
    reportItem: ReportItem,
    masterItem: ReportMasterItem,
  ) => {
    // 下载归档被点击了
    message.loading('归档并下载中，请稍后');
    if (isNil(reportBaseInfo?.LatestArchive)) {
      setReportArchiveLoading(true);
      let archiveResponse = await archiveReport(
        reportItem?.ReportSettingMasterId,
        reportItem?.ReportBaseId,
      );
      if (archiveResponse?.code === 0 && archiveResponse?.statusCode === 200) {
        reportBaseInfoArchiveReq(
          reportItem?.ReportSettingMasterId,
          reportItem?.ReportBaseId,
        );
      } else {
        message.error('归档发生错误，请稍后重试');
        setReportArchiveLoading(false);
      }
    } else {
      downloadReportByBlobId(
        reportBaseInfo?.LatestArchive,
        `${reportBaseInfo?.Title}-${dayjs().format('YYYYMMDD_HHmmss')}`,
      );
    }
  };

  const stopPolling = () => {
    reportBaseInfoArchiveCancel();
  };

  // reportBaseInfo
  const { run: reportBaseInfoReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/GetReportBaseById', {
        method: 'POST',
        data: {
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ReportBaseInfo>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          setReportBaseInfo(response?.data);
          return response?.data;
        } else {
          setReportBaseInfo({});
          return null;
        }
      },
    },
  );

  // archive and download data
  useEffect(() => {
    if (reportArchiveBaseInfo?.ReportArchiveStatus === '100') {
      reportBaseInfoArchiveCancel();
      setReportArchiveLoading(false);
      downloadReportByBlobId(
        reportArchiveBaseInfo?.LatestArchive,
        `${reportArchiveBaseInfo?.Title}-${dayjs().format('YYYYMMDD_HHmmss')}`,
      );
    } else if (reportArchiveBaseInfo?.ReportArchiveStatus === '999') {
      reportBaseInfoArchiveCancel();
      message.error('归档出错，请联系管理员');
      setReportArchiveLoading(false);
      return;
    }

    setReportBaseInfo(reportArchiveBaseInfo);
  }, [reportArchiveBaseInfo]);

  const lockUnlockReportChangeColumns = (lock: boolean) => {
    let reportColumns = reportTableColumns?.slice();
    let operationItemIndex = reportColumns?.findIndex(
      (item) => item?.dataIndex === 'operation',
    );
    if (operationItemIndex > -1) {
      reportColumns = reportColumns?.slice(0, operationItemIndex);
    }

    if (!lock) {
      reportColumns = [...reportColumns];
    }

    setReportTableColumns(reportColumns);
  };

  const { run: reportBaseInfoArchiveReq, cancel: reportBaseInfoArchiveCancel } =
    useRequest(
      (masterId, reportId) => {
        return uniCommonService('Api/Report/Report/GetReportBaseById', {
          method: 'POST',
          data: {
            ReportBaseId: reportId,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        pollingInterval: 1000,
        pollingWhenHidden: false,
        formatResult: async (response: RespVO<ReportBaseInfo>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            setReportArchiveBaseInfo(response?.data);
          }
        },
      },
    );

  // report data update
  const { loading: reportDataUpdateLoading, run: reportDataUpdateReq } =
    useRequest(
      (masterId, reportId, tableData) => {
        return uniCommonService('Api/Report/Report/Update', {
          method: 'POST',
          data: {
            ReportBaseId: reportId,
            reportSettingMasterId: masterId,
            data: tableData,
          },
          requestType: 'json',
        });
      },
      {
        manual: true,
        formatResult: async (response: RespVO<any>) => {
          return response;
        },
      },
    );

  // columns
  const { run: reportColumnsReq } = useRequest(
    (id) => {
      return uniCommonService('Api/Report/Report/GetReportDataTablesColumns', {
        params: {
          ReportSettingMasterId: id,
        },
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<ColumnItem[]>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          let transformedTableColumns = tableColumnBaseProcessor(
            // masterItem?.DataSize === ReportDataSize.SingleLine || masterItem?.IsReadonly
            masterItem?.IsReadonly || reportBaseInfo?.IsLocked ? [] : [],
            transformReportColumnsWithDataTypeIntOrderable(
              masterItem?.ReportMode,
              response?.data,
            ),
          );

          if (hasPivotColumn(transformedTableColumns)) {
            pivotServiceInstance.setTableColumns(transformedTableColumns);

            return [];
          }

          return reportTableGroupNameHeaderProcessor(
            transformedTableColumns.map((item) => {
              return {
                ...item,
                readonly: masterItem?.IsReadonly || item?.isReadOnly,
                // formItemProps: {
                //   rules: [
                //     {
                //       required: !item?.isNullable,
                //       whitespace: false,
                //       message: '此项是必填项',
                //     },
                //   ],
                // },
              };
            }),
          );
        } else {
          return [];
        }
      },
    },
  );

  const { run: reportBaseInfoDataLockReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/Lock', {
        method: 'POST',
        data: {
          ReportSettingMasterId: masterId,
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const { run: reportBaseInfoDataUnlockReq } = useRequest(
    (masterId, reportId) => {
      return uniCommonService('Api/Report/Report/UnLock', {
        method: 'POST',
        data: {
          ReportSettingMasterId: masterId,
          ReportBaseId: reportId,
        },
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const reportDataProcessor = async (
    data: any[],
    params: any[],
    saveCondition = false,
  ) => {
    // TODO 是否需要显示数据
    let tableData = data?.map((item, index) => {
      return {
        rowId: uuidv4(),
        // rowId: index,
        ...item,
      };
    });
    setReportTableDataSource(tableData);

    if (pivotServiceInstance?.hasPivotColumns === true) {
      let pivotData = pivotServiceInstance.pivotColumnsDataTransformer(
        tableData,
        [],
      );

      setReportTableColumns(pivotData?.columns);
      setReportTableDataSource(pivotData?.dataSources ?? []);
    }

    setReportLoading(false);

    // 先更新数据再验证
    let reportDataProcessorBaseInfo = await reportBaseInfoReq(
      reportBaseInfo?.ReportSettingMasterId,
      reportBaseInfo?.ReportBaseId,
    );

    if (
      masterItem?.EnableValidate &&
      reportDataProcessorBaseInfo?.ReportValidateStatus === '0'
    ) {
      await validateReportData(reportDataProcessorBaseInfo);
    }
  };

  const { run: reportDataReq } = useRequest(
    (masterId, reportId, reload) => {
      let data = {
        ReportSettingMasterId: masterId,
        ReportBaseId: reportId,
        Reload: reload || false,
      };

      return uniCommonService('Api/Report/Report/Query', {
        method: 'POST',
        data: data,
        requestType: 'json',
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any[]>) => {
        return response;
      },
      onSuccess: (response, params) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          reportDataProcessor(response?.data || [], params);
        } else {
          setReportTableDataSource([]);
          setReportLoading(false);
        }
      },
    },
  );

  const validateReportData = async (
    reportDataProcessorBaseInfo: ReportBaseInfo,
  ) => {
    let syncValidateResponse: RespVO<ValidationItem[]> =
      await syncValidateReport(
        reportDataProcessorBaseInfo?.ReportSettingMasterId,
        reportDataProcessorBaseInfo?.ReportBaseId,
      );

    if (
      syncValidateResponse?.code === 0 &&
      syncValidateResponse?.statusCode === 200
    ) {
      if (syncValidateResponse?.data?.length === 0) {
        message.success('报表数据校验通过');
        setReportValidateFailureOpen(false);
      } else if (syncValidateResponse?.data?.length > 0) {
        // TODO validation 展示
        message.error('报表数据校验出现问题，具体请看表格');
        setReportValidateFailureOpen(true);
      }
      setValidationFailureData(syncValidateResponse?.data ?? []);
    } else {
      message.error('报表数据校验失败，请稍后重试');
    }
  };

  const onTableDataSourceSave = async () => {
    let isValidate = true;
    let currentDataSource = cloneDeep(reportTableDataSource);
    Object.entries({
      ...form.getFieldsValue(),
      ...(tableEditingData?.current ?? {}),
    })?.map((items) => {
      let oldDataItemIndex = currentDataSource?.findIndex(
        (item) => item?.rowId == items?.at(0),
      );
      if (oldDataItemIndex > -1) {
        let oldDataItem = currentDataSource?.at(oldDataItemIndex);
        if (!isEmptyValues(oldDataItem)) {
          currentDataSource[oldDataItemIndex] = {
            ...oldDataItem,
            ...(items?.at(1) as any),
          };
        }
      }
    });

    currentDataSource?.forEach((item) => {
      reportTableColumns
        ?.filter((item) => !item?.isNullable)
        ?.forEach((columnItem) => {
          if (columnItem?.data) {
            if (isNil(item?.[columnItem?.data])) {
              isValidate = false;
            }
          }
        });
    });

    setReportTableDataSource(currentDataSource);

    if (isValidate) {
      message.success('保存中....');
      setReportLoading(true);
      let updateResponse = await reportDataUpdateReq(
        reportBaseInfo?.ReportSettingMasterId,
        reportBaseInfo?.ReportBaseId,
        currentDataSource,
      );
      setReportLoading(false);

      if (updateResponse?.code === 0 && updateResponse?.statusCode === 200) {
        message.success('保存成功');
        setEditableColumnKeys([]);
        // validate
        if (masterItem?.EnableValidate) {
          await validateReportData(reportBaseInfo);
        }
      } else {
        message.error('保存失败');
      }
    } else {
      let key = uuidv4();
      notification.warning({
        message: '表格更新校验不通过',
        description: '请检查是否有必填字段未填',
        key: key,
      });
    }
  };

  const tableY =
    document.getElementById('report-content-container')?.offsetHeight -
    (document.getElementsByClassName('ant-table-thead')?.[0]?.clientHeight ||
      0) -
    64 -
    64 -
    (document.getElementById('report-title')?.offsetHeight || 0) -
    20 -
    10 -
    45;

  return (
    <div
      id={'stats-table-persist-container'}
      className={'stats-table-persist-container'}
    >
      <div className={'flex-row'} style={{ flex: 1 }}>
        <Card
          id={'report-content-container'}
          className={'report-content-container'}
          style={{
            width: `${reportValidateFailureOpen ? '60%' : 'calc(100% - 40px)'}`,
          }}
          title={reportBaseInfo?.Title}
          extra={
            <>
              <Space>{processOperationBtns()}</Space>
            </>
          }
        >
          <UniEditableTable
            actionRef={actionRef}
            id={'report-table'}
            rowKey={'rowId'}
            enableShortcuts={false}
            enableHotKeys={true}
            style={{
              '--tableMinHeight': `${Math.max(tableY ?? 300, 300)}px`,
            }}
            scroll={{
              x: 'max-content',
              y: Math.max(tableY ?? 200, 200),
            }}
            widthCalculate={true}
            widthDetectAfterDictionary
            forceColumnsUpdate={true}
            columns={reportTableColumns}
            value={reportTableDataSource}
            bordered={true}
            loading={reportLoading}
            pagination={false}
            dictionaryData={globalState?.dictData}
            onTableChange={frontTableOnChange}
            // recordCreatorProps={{
            //   record: {
            //     rowId: uuidv4(),
            //   },
            //   newRecordType: 'dataSource',
            // }}
            recordCreatorProps={false}
            editable={{
              form: form,
              type: 'multiple',
              editableKeys: editableColumnKeys,
              formProps: {
                onValuesChange: (changedValues, allValues) => {
                  console.log('onValuesChange', changedValues, allValues);
                },
              },
              onSave: async (rowKey, data, row) => {
                console.log(rowKey, data, row);
              },
              actionRender: (row, config, defaultDoms) => {
                return [defaultDoms.save, defaultDoms.cancel];
              },
              onValuesChange: (record, recordList) => {
                // setReportTableDataSource(recordList);
                // 多存一份 改动记录
                tableEditingData.current[record?.rowId] = record;

                console.log(
                  'onValuesChange',
                  record,
                  recordList,
                  form.getFieldsValue(),
                );
              },
              onChange: setEditableColumnKeys,
            }}
            {...merge(
              {},
              virtualConfig('report-table', reportTableDataSource?.length),
              { components: components },
            )}
          />
        </Card>

        <div
          className={`validation-content-container ${
            reportValidateFailureOpen ? 'container-open' : 'container-close'
          }`}
        >
          {/* <div className={'validation-trigger-container'}>
            <span
              className={'label'}
              onClick={() => {
                setReportValidateFailureOpen(!reportValidateFailureOpen);
              }}
            >
              错误报告
            </span>
          </div> */}

          <div
            className={`validation-table-content ${
              reportValidateFailureOpen ? 'content-open' : 'content-close'
            }`}
          >
            <ValidationResult
              title={reportBaseInfo?.Title}
              reportBaseInfo={reportBaseInfo}
              validationData={validationFailureData}
              height={tableY}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatsReportPersistTable;
