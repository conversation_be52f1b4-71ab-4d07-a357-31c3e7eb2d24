import {
  IRowSelection,
  ITableState,
  IModalState,
  ISorter,
  IStatState,
} from './interface';

export enum TableAction {
  init = 'INIT',
  columnsChange = 'COLUMNSCHANGE',
  dataPush = 'DATAPUSH',
  dataChange = 'DATACHANGE',
  selectionChange = 'SELECTIONCHANGE',
  sortChange = 'SORTCHANGE',
  clkChange = 'CLKITEMCHANGE',
}

export enum StatAction {
  init = 'INIT',
  columnsChange = 'COLUMNSCHANGE',
  dataChange = 'DATACHANGE',
}

export enum ModalAction {
  init = 'INIT',
  change = 'CHANGE',
}

// table init data
const InitTableState: ITableState<any> = {
  columns: [],
  data: [],
  total: 0,
};

// stat init data
const InitStatState: IStatState<any> = {
  columns: [],
  data: [],
  compareStatRes: {
    Loms: [],
    Loys: [],
    Moms: [],
    Yoys: [],
  },
};

const tableDataReducer = (
  state: ITableState<any> & IRowSelection<any> & ISorter<any>,
  action: { type: TableAction; payload: any },
) => {
  let { type, payload } = action;
  switch (type) {
    case TableAction.init:
      return { ...state, ...payload };
    case TableAction.columnsChange:
      return { ...state, columns: payload.columns };
    case TableAction.dataPush:
      return { ...state, data: [...state.data].concat(payload.data) };
    case TableAction.dataChange:
      return { ...state, data: payload.data };
    case TableAction.selectionChange:
      return {
        ...state,
        selectedKeys: payload.selectedKeys,
        selectedRecords: payload.selectedRecords,
      };
    case TableAction.sortChange:
      return {
        ...state,
        sorter: payload.sorter,
      };
    case TableAction.clkChange:
      return {
        ...state,
        clkItem: payload.clkItem,
      };
    default:
      throw new Error('tableDataReducer需要一个type 或者 type非法');
  }
};

const statDataReducer = (
  state: IStatState<any>,
  action: { type: StatAction; payload: any },
) => {
  let { type, payload } = action;
  switch (type) {
    case StatAction.init:
      return { ...state, ...InitStatState };
    case StatAction.columnsChange:
      return { ...state, columns: payload.columns };
    case StatAction.dataChange:
      return {
        ...state,
        data: payload.data,
        compareStatRes: payload?.compareStatRes ?? state.compareStatRes,
      };
    default:
      throw new Error('statDataReducerr需要一个type 或者 type非法');
  }
};

const modalReducer = (
  state: IModalState<any>,
  action: { type: ModalAction; payload: any },
) => {
  let { type, payload } = action;
  switch (type) {
    case ModalAction.init:
      return {
        visible: false,
        record: undefined,
        actionType: undefined,
      };
    case ModalAction.change:
      return { ...state, ...payload };
    default:
      throw new Error('modalReducer需要一个type 或者 type非法');
  }
};

export {
  InitTableState,
  InitStatState,
  tableDataReducer,
  statDataReducer,
  modalReducer,
};
