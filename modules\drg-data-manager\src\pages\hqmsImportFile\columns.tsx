import React from 'react';
import { Emitter } from '@uni/utils/src/emitter';
import { EventConstants } from './constants';
import IconBtn from '@uni/components/src/iconBtn/index';

/**
 * 定义前端的HQMS上传记录列配置
 * 这里配置的列会与后端返回的列合并
 */
export const HqmsUploadRecordColumns = [
  {
    data: 'options',
    dataIndex: 'options',
    title: '',
    width: 54,
    align: 'center',
    fixed: 'left',
    visible: true,
    render: (_, record) => (
      <IconBtn
        type="check"
        title="查看"
        onClick={() => {
          Emitter.emit(EventConstants.HQMS_UPLOAD_RECORD_VIEW_DETAIL, record);
        }}
      />
    ),
  },
];
