import theme from '@uni/components/src/echarts/themes/themeBlueYellow';

import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import { valueNullOrUndefinedReturnDash } from '@uni/utils/src/utils';

import _ from 'lodash';

const BarCharts = (
  data,
  category = null, // x轴
  valueKey = null, // y轴
  yAxis = null, // y轴
) => {
  if (_.isEmpty(data) || !category || !valueKey) {
    return {};
  }
  if (valueKey) {
    data = _.orderBy(data, valueKey, 'desc');
  }
  let color = theme.color;
  let option = {
    dataset: {
      source: data,
    },
    tooltip: {
      trigger: 'axis',
    },
    legend: {
      show: true,
    },
    grid: {},
    xAxis: {
      type: 'category',
      scale: true,
      boundaryGap: true,
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: 4,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: 4,
      },
    ],
    yAxis: {
      name: yAxis || '出院人次',
      position: 'left',
      splitLine: {
        show: true,
      },
    },
    series: [
      {
        name: yAxis || '出院人次',
        type: 'bar',
        barWidth: 10,
        label: {
          normal: {
            show: true,
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            opacity: 1,
            color: {
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 1,
                  color: color[4], // 100% 处的颜色
                },
                {
                  offset: 0,
                  color: color[0], // 0% 处的颜色
                },
              ],
              globalCoord: false, // 缺省为 false
            },
          },
        },
        encode: {
          y: valueKey,
          x: category,
          tooltip: valueKey,
        },
      },
    ],
  };

  return option;
};

export { BarCharts };
