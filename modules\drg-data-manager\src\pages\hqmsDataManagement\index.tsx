import React, { useState } from 'react';
import { Tabs } from 'antd';
import MonthlyDataTab from './components/MonthlyDataTab';
import UserDataCnt from './components/UserDataCnt/index';
import QualityControlTab from './components/QualityControlTab';
import { useModel } from 'umi';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

const { TabPane } = Tabs;

const HqmsDataManagement: React.FC = () => {
  // 使用全局状态获取字典数据
  const { globalState } = useModel('@@qiankunStateFromMaster');
  const { searchParams, dictData } = globalState;

  // 保存月度数据的自定义列配置
  const [monthlyCustomColumns, setMonthlyCustomColumns] = useState<any[]>([]);

  // Tab切换处理
  const handleTabChange = (activeKey: string) => {
    // 可以根据需要在Tab切换时做一些操作
  };

  // 处理列设置保存成功回调
  const handleTableColumnSave = (newColumns: any[]) => {
    // 处理新的列配置，可能需要进行一些转换
    const processedColumns = tableColumnBaseProcessor([], newColumns);
    // 更新自定义列状态
    setMonthlyCustomColumns(processedColumns);
    console.log('列配置保存成功', processedColumns);
  };

  // 创建tabBarExtraContent对象
  const tabBarExtraContent = {
    right: (
      <TableColumnEditButton
        columnInterfaceUrl="Api/Hqms/MedQualityReport/GetMonthlyDatas"
        onTableRowSaveSuccess={handleTableColumnSave}
      />
    ),
  };

  return (
    <Tabs
      defaultActiveKey="1"
      onChange={handleTabChange}
      // tabBarExtraContent={tabBarExtraContent}
    >
      <TabPane tab="月度数据" key="1">
        <UserDataCnt />
        {/* <MonthlyDataTab
          searchParams={searchParams}
          dictData={dictData}
          customColumns={monthlyCustomColumns}
        /> */}
      </TabPane>
      <TabPane tab="质控结果" key="2">
        <QualityControlTab searchParams={searchParams} dictData={dictData} />
      </TabPane>
    </Tabs>
  );
};

export default HqmsDataManagement;
