import { Effect, ImmerReducer, Subscription } from 'umi';
import _ from 'lodash';
import { uniHqmsService as uniCommonService } from '@uni/services/src';
import { fetchTableParamsHandler, isRespErr } from '@/utils/widgets';
import { ReqActionType } from '@/constants';
type EffectType = 'takeEvery' | 'takeLatest' | 'watcher' | 'throttle';
type EffectWithType = [Effect, { type: EffectType }];
interface RequestParams {
  url: string;
  fetchColumn?: boolean;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  requestType?: 'form' | 'json';
  params?: any;
  data?: any;
  headers?: object;
  responseType?: 'json' | 'blob';
  dataType?: 'normal' | 'mr';
  current?: number;
  pageSize?: number;
}

export interface IPayloadProps {
  name: string; // 唯一标识符
  requestParams: RequestParams | RequestParams[];
  saveInRedux?: string;
}
export interface IGlobalProps {
  //...
}

export interface IGlobalModelType {
  //...
  namespace: 'global';
  state: {
    items: any;
    columns: any;
    loadings: {
      [key: string]: boolean;
    };
  };
  effects: {
    fetchColumns: Effect;
    fetchNormal: EffectWithType;
    fetchDataTable: Effect;
    fetchTableDataLatest: EffectWithType;
    fetchTableDataEvery: EffectWithType;
  };
  reducers: {
    getData: ImmerReducer<any>;
    saveFetched: ImmerReducer<any>;
    saveColumns: ImmerReducer<any>;
    changeLoading: ImmerReducer<any>;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const GlobalModel: IGlobalModelType = {
  namespace: 'global',
  state: {
    items: {},
    columns: {},
    loadings: {},
  },
  effects: {
    *fetchColumns({ payload: { requestList } }, { call, put, select, all }) {
      const tableColumns = (yield select()).global.columns;
      let columnsList = {};
      let fetchColumns = [],
        fetchRequestList = [];

      requestList.forEach((d) => {
        if (!tableColumns[d.name]) {
          fetchColumns.push(d.name);
          fetchRequestList.push(
            call(uniCommonService, d.url, {
              headers: {
                ...(d.headers ?? {}),
                'Retrieve-Column-Definitions': 1,
              },
              method: 'POST',
              isMr: d?.dataType === 'mr',
            }),
          );
        }
      });

      console.log(tableColumns);
      if (fetchRequestList.length > 0) {
        let res = yield all(fetchRequestList);

        for (let i = 0; i < fetchColumns.length; i++) {
          const item = fetchColumns[i];
          const respItem = res[i];
          if (!isRespErr(respItem)) {
            if (respItem?.data) {
              columnsList[item] = (respItem?.data?.Columns ?? [])?.map(
                (col: any) => {
                  return {
                    ...col,
                    sorter: col.sorter || false,
                    dataIndex: col.data,
                    defaultRenderByType: col.defaultRenderByType || true,
                  };
                },
              );
            }
          }
        }

        yield put({
          type: 'saveColumns',
          payload: {
            columnsList,
          },
        });
      }
    },
    fetchNormal: [
      function* (
        { payload: { name, requestParams, saveInRedux } },
        { call, put, select, all },
      ) {
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: true,
          },
        });
        // 根据payload来处理：
        let res = yield call(uniCommonService, requestParams.url, {
          ...requestParams,
          isMr: requestParams?.dataType === 'mr',
        });
        console.log(res);
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: false,
          },
        });

        if (isRespErr(res)) return res;

        if (saveInRedux) {
          yield put({
            type: 'saveFetched',
            payload: {
              name: saveInRedux,
              value: res,
            },
          });
        }

        return res;
      },
      { type: 'takeEvery' },
    ],
    *fetchDataTable(
      { payload: { requestParams, saveInRedux } },
      { call, put, select, all },
    ) {
      // const tableColumns = (yield select()).global.columns[name];
      // let finalColumns = tableColumns;
      // // columns part( 把columns fetch 移动到 subscription 里面了,不打算在这里处理了)
      // if (!finalColumns && requestParams.fetchColumn) {
      //   let res = yield call(uniCommonService, requestParams.url, {
      //     ...requestParams,
      //     isMr: requestParams?.dataType === 'mr',
      //     headers: {
      //       ...(requestParams.headers ?? {}),
      //       'Retrieve-Column-Definitions': 1,
      //     },
      //   });

      //   if (isRespErr(res)) {
      //     return res;
      //   }

      //   let { data: resData } = res;
      //   finalColumns = (resData?.Columns ?? [])?.map((col: any) => {
      //     return {
      //       ...col,
      //       sorter: col.sorter || false,
      //       dataIndex: col.data,
      //       defaultRenderByType: col.defaultRenderByType || true,
      //     };
      //   });
      //   yield put({
      //     type: 'saveColumns',
      //     payload: {
      //       name,
      //       value: finalColumns,
      //     },
      //   });
      // }
      // data part
      let fetchRequestList = [];
      if (Array.isArray(requestParams)) {
        requestParams.forEach((d) => {
          fetchRequestList.push(
            call(uniCommonService, d.url, fetchTableParamsHandler(d)),
          );
        });
      } else {
        // TODO: 单条时的total bug
        fetchRequestList.push(
          call(
            uniCommonService,
            requestParams.url,
            fetchTableParamsHandler(requestParams),
          ),
        );
      }
      if (fetchRequestList.length > 0) {
        let res;
        res = yield all(fetchRequestList);
        console.log(res);
        let resDatas = res.map((d, i) => {
          if (!isRespErr(d)) {
            let { data: resData } = d;
            return {
              data: resData?.data ?? resData?.Items ?? resData,
              total:
                (requestParams[i].filterData
                  ? resData?.recordsFiltered
                  : resData?.recordsTotal) ??
                resData?.TotalCount ??
                resData?.length ??
                0,
            };
          } else {
            return d;
          }
        });
        return resDatas.map((d, i) => ({
          name: requestParams[i].name,
          ...d,
        }));
      }
      return { code: -1, message: 'noFetchList' };
    },
    fetchTableDataLatest: [
      function* (
        { payload: { name, requestParams, saveInRedux } },
        { call, put, select },
      ) {
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: true,
          },
        });
        const res = yield put.resolve({
          type: 'fetchDataTable',
          payload: { requestParams, saveInRedux },
        });
        // console.log(res);
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: false,
          },
        });
        return res;
      },
      { type: 'takeLatest' },
    ],
    fetchTableDataEvery: [
      function* (
        { payload: { name, requestParams, saveInRedux } },
        { call, put, select },
      ) {
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: true,
          },
        });
        const res = yield put.resolve({
          type: 'fetchDataTable',
          payload: { ...requestParams, ...saveInRedux },
        });
        // loading
        yield put({
          type: 'changeLoading',
          payload: {
            name,
            value: false,
          },
        });
        return res;
      },
      { type: 'takeEvery' },
    ],
  },
  reducers: {
    getData(state, { payload: { name, type } }) {
      return state[type];
    },
    saveFetched(state, { payload: { name, value } }) {
      state.items[name] = value;
    },
    saveColumns(state, { payload: { name, value, columnsList } }) {
      if (columnsList) {
        state.columns = { ...state.columns, ...columnsList };
      }
      if (name) {
        state.columns[name] = value;
      }
    },
    changeLoading(state, { payload: { name, value } }) {
      state.loadings[name] = value;
    },
  },
  subscriptions: {
    setup({ dispatch, history }, done) {
      return history.listen(({ pathname }) => {
        dispatch({
          type: 'fetchColumns',
          payload: {
            requestList: [
              {
                name: ReqActionType.HospDrgsData,
                method: 'POST',
                url: `Api/Drgs/${ReqActionType.HospDrgsData}`,
              },
              {
                name: ReqActionType.HospOperData,
                method: 'POST',
                url: `Api/Drgs/${ReqActionType.HospOperData}`,
              },
              {
                name: ReqActionType.HospSdData,
                method: 'POST',
                url: `Api/Drgs/${ReqActionType.HospSdData}`,
              },
            ],
          },
        });
        // 进来就调
        // dispatch({
        //   type: 'fetchColumns',
        //   payload: {
        //     requestList: Object.keys(ReqActionType).map((d) => {
        //       return ReqActionType[d].indexOf('Details') === -1
        //         ? {
        //             url: `Api/Drgs/${ReqActionType[d]}`,
        //             method: 'POST',
        //             name: ReqActionType[d],
        //           }
        //         : {
        //             url: `Api/Drgs/${ReqActionType[d]}`,
        //             method: 'POST',
        //             name: ReqActionType[d],
        //           };
        //     }),
        //   },
        // });
        // switch(pathname) {
        //   case: '/'
        // }
        // switch (pathname) {
        //   case '/inpatient':
        //     break;
        //   case '/obspatient':
        //   case '/outpatient':
        //   case '/hierarchyBed':
        //   case '/medLab':
        //     break;
        //   default:
        //     break;
        // }
      });
    },
  },
};

export default GlobalModel;
