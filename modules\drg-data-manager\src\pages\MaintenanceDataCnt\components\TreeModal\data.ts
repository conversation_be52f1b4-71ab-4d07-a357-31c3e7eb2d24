import type { DataNode } from 'antd/es/tree';
export const treeData: DataNode[] = [
  {
    title: '绩效',
    key: 'DrgsMetrics',
    children: [
      { title: '联众DRGs', key: 'DrgsMetrics_DrgsAndSd' },
      { title: '自定义单病种', key: 'DrgsMetrics_CustomSd' },
      { title: '手术', key: 'DrgsMetrics_Oper' },
      { title: '医疗质量', key: 'DrgsMetrics_Quality' },
    ],
  },
  {
    title: '国考',
    key: 'HqmsMetrics',
    children: [
      { title: '手术', key: 'HqmsMetrics_Surgery' },
      { title: '单病种', key: 'HqmsMetrics_Sd' },
      { title: '国考DRGs', key: 'HqmsMetrics_HqmsDrg' },
      { title: '并发症', key: 'HqmsMetrics_Complication' },
      { title: '医疗质量', key: 'HqmsMetrics_Quality' },
    ],
  },
  {
    title: '等评',
    key: 'GradeMetrics',
    children: [
      { title: '单病种', key: 'GradeMetrics_Sd' },
      { title: '并发症', key: 'GradeMetrics_Complication' },
      { title: '医疗质量', key: 'GradeMetrics_Quality' },
    ],
  },
];

export function filterMenuData(menuData, params) {
  const {
    EnableDrgsMetrics,
    EnableGradeMetrics,
    EnableHqmsMetrics,
    EnableHqmsDrg,
    EnableCustomDrg,
  } = params || {};
  // 过滤顶层菜单项
  const filteredMenu = menuData.filter((item) => {
    if (item.key === 'DrgsMetrics') return EnableDrgsMetrics;
    if (item.key === 'HqmsMetrics') return EnableHqmsMetrics;
    if (item.key === 'GradeMetrics') return EnableGradeMetrics;
    return true; // 其他项默认保留
  });

  // 遍历过滤后的菜单项，处理子菜单
  return filteredMenu.map((item) => {
    // 特殊处理子菜单
    if (item.key === 'DrgsMetrics' && item.children) {
      item.children = item.children.filter((child) => {
        if (child.key === 'DrgsMetrics_CustomSd') return EnableCustomDrg;
        return true; // 其他子项默认保留
      });
    }

    if (item.key === 'HqmsMetrics' && item.children) {
      item.children = item.children.filter((child) => {
        if (child.key === 'HqmsMetrics_HqmsDrg') return EnableHqmsDrg;
        return true; // 其他子项默认保留
      });
    }

    return item;
  });
}
