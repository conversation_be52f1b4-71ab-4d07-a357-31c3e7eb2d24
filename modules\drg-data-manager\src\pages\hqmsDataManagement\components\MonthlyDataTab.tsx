import React, { useMemo, useEffect, useState } from 'react';
import { useRequest } from 'umi';
import { message } from 'antd';
import { UniTable } from '@uni/components/src';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';

// 月度数据的接口
interface MonthlyData {
  Id: string;
  Month: string;
  TotalCount: number;
  QualityCount: number;
  QualityRate: string;
  [key: string]: any;
}

interface MonthlyDataTabProps {
  searchParams?: any;
  dictData?: any;
  customColumns?: any[]; // 自定义列配置
}

const MonthlyDataTab: React.FC<MonthlyDataTabProps> = ({
  searchParams,
  dictData,
  customColumns,
}) => {
  // 获取表格列定义
  const { data: columns, mutate: setColumns } = useRequest(
    () =>
      uniCommonService('Api/Hqms/MedQualityReport/GetMonthlyDatas', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res: any) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return tableColumnBaseProcessor([], res.data?.Columns);
        }
        return [];
      },
    },
  );

  // 构建请求参数
  const buildRequestParams = () => {
    return {
      sdate: searchParams?.dateRange?.[0],
      edate: searchParams?.dateRange?.[1],
      hospCode: searchParams?.hospCodes || [],
      searchKeyWord: searchParams?.searchKeyWord || '',
    };
  };

  // 获取月度数据列表
  const {
    data: tableData,
    loading,
    run: fetchData,
  } = useRequest(
    (params) =>
      uniCommonService('Api/Hqms/MedQualityReport/GetMonthlyDatas', {
        method: 'POST',
        data: {
          ...buildRequestParams(),
          ...params,
        },
      }),
    {
      manual: true,
      formatResult: (res: any) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data || [];
        }
        return [];
      },
    },
  );

  // 首次加载数据及searchParams变化时重新加载数据
  useEffect(() => {
    // 传入当前的搜索参数
    fetchData({});
  }, [searchParams]);

  // 当自定义列更新时，更新当前列配置
  useEffect(() => {
    if (customColumns && customColumns.length > 0) {
      setColumns(customColumns);
    }
  }, [customColumns]);

  // 表格列配置
  const tableColumns = useMemo(() => {
    // 使用自定义列或默认列
    const baseColumns = columns || [];

    // 添加操作列
    const actionColumn = {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      fixed: 'right',
      render: (_: any, record: MonthlyData) => (
        <a
          onClick={() => {
            // 查看月度数据统计详情
            message.info('查看月度数据统计详情，功能待实现');
          }}
        >
          统计详情
        </a>
      ),
    };

    // 检查baseColumns中是否已包含操作列
    const hasActionColumn = baseColumns.some(
      (col) => col.dataIndex === 'action',
    );

    return hasActionColumn ? baseColumns : [...baseColumns, actionColumn];
  }, [columns]);

  return (
    <UniTable
      id="hqms-monthly-data-table"
      rowKey="Id"
      columns={tableColumns}
      dataSource={tableData}
      loading={loading}
      scroll={{ x: 'max-content' }}
      dictionaryData={dictData}
      className="hqms-monthly-data-table"
      widthDetectAfterDictionary={true}
    />
  );
};

export default MonthlyDataTab;
