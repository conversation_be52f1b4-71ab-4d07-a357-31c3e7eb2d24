import theme from '@uni/components/src/echarts/themes/themeBlueYellow';
import { echarts } from '@uni/components/src/echarts';
import { getLinebreakFormat } from '@uni/components/src/echarts/echarts.utils';
import {
  valueNullOrUndefinedReturnDash,
  valueNullOrUndefinedReturnDashWithDictionaryModule,
} from '@uni/utils/src/utils';
import _ from 'lodash';
import { ELEMENT_CONFIG } from './constants';

// 变化对比
// category = Coder CliDept Chief
const StatsSelectedTrendsLineOption = (
  data,
  category,
  selectedItem,
  dictData,
) => {
  if (_.isEmpty(data)) {
    return {};
  }
  // 处理差值 & 排序 & 翻译
  // 如果selectedItem 的 metricName 里面包含 diff 的话 则自身就是差值 不展示编码前/编码后

  data = _.orderBy(
    data?.map((d) => ({
      ...d,
      [`${category}Name`]:
        dictData?.find((dict) => dict?.Code === d?.[category])?.Name ??
        d?.[category] ??
        '-',
      selectedDiffCnt:
        d?.[selectedItem?.ColumnName] - d?.[selectedItem?.preData?.ColumnName],
    })),
    selectedItem?.ColumnName,
    'desc',
  );
  console.log('inchart', data, category, selectedItem, dictData);

  let color = theme.color;
  let length = 4;
  let option = {
    dataset: {
      source: data,
    },
    grid: {
      x: '13%',
      x2: '5%',
    },
    legend: {
      icon: 'circle',
      show: true,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let value = params[0]?.data;
        if (value) {
          return (
            `${ELEMENT_CONFIG?.[category]?.title}：${
              value?.[`${category}Name`]
            }<br /> ` +
            `编码后：${valueNullOrUndefinedReturnDash(
              value?.[selectedItem?.ColumnName],
              selectedItem?.dataType,
            )}<br /> ` +
            (selectedItem?.ColumnName?.toLowerCase()?.includes('diff')
              ? ''
              : `编码前：${valueNullOrUndefinedReturnDash(
                  value?.[`Pre${selectedItem?.ColumnName}`],
                  selectedItem?.dataType,
                )}`)
          );
        }
      },
    },
    dataZoom: [
      {
        type: 'slider',
        height: 20,
        bottom: '5%',
        xAxisIndex: 0,
        showDetail: false,
        startValue: 0, //数值index
        endValue: length,
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        startValue: 0,
        endValue: length,
      },
    ],
    xAxis: {
      type: 'category',
      axisLabel: {
        // formatter: (value, index) => {
        //   return valueNullOrUndefinedReturnDash(, 'Month');
        // },
      },
    },
    yAxis: [
      {
        name: `${
          typeof selectedItem?.title === 'string'
            ? selectedItem?.title
            : selectedItem?.ColumnTitle || selectedItem?.Title || ''
        }`,
        type: 'value',
        position: 'left',
        splitLine: {
          show: true,
        },
      },
      {
        name: '差值',
      },
    ],
    series: [
      {
        name: selectedItem?.ColumnName?.toLowerCase()?.includes('diff')
          ? '差值'
          : '编码后',
        type: 'bar',
        barWidth: 10,
        // symbol: 'none',
        smooth: true,
        label: {
          normal: {
            show: true,
            formatter: (params) => {
              let value = params?.data;
              return `${valueNullOrUndefinedReturnDash(
                value?.[selectedItem?.ColumnName],
                selectedItem?.dataType,
              )}`;
            },
          },
        },
        // lineStyle: {
        //   normal: {
        //     opacity: 1,
        //     width: 3,
        //     color: color[3],
        //   },
        // },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            color:
              category === 'Coder'
                ? color[0]
                : category === 'CliDept'
                ? color[3]
                : category === 'Chief'
                ? color[4]
                : color[2],
          },
        },
        encode: {
          x: `${category}Name`,
          y: [selectedItem?.ColumnName],
          tooltip: [selectedItem?.ColumnName],
        },
      },
      // 编码前
      !selectedItem?.ColumnName?.toLowerCase()?.includes('diff') && {
        name: `编码前`,
        type: 'bar',
        barWidth: 10,
        show: !selectedItem?.ColumnName?.toLowerCase()?.includes('diff'),
        // symbol: 'none',
        smooth: true,
        label: {
          normal: {
            show: !selectedItem?.ColumnName?.toLowerCase()?.includes('diff'),
            formatter: (params) => {
              let value = params?.data;
              return `${valueNullOrUndefinedReturnDash(
                value?.[selectedItem?.preData?.ColumnName],
                selectedItem?.dataType,
              )}`;
            },
          },
        },
        // lineStyle: {
        //   normal: {
        //     opacity: 1,
        //     width: 3,
        //     color: color[3],
        //   },
        // },
        itemStyle: {
          normal: {
            barBorderRadius: [5, 5, 0, 0],
            color: '#d9d9d9',
          },
        },
        encode: {
          x: `${category}Name`,
          y: [selectedItem?.preData?.ColumnName],
          tooltip: [selectedItem?.preData?.ColumnName],
        },
      },
      !selectedItem?.ColumnName?.toLowerCase()?.includes('diff') && {
        name: `差值`,
        type: 'line',
        show: !selectedItem?.ColumnName?.toLowerCase()?.includes('diff'),
        yAxisIndex: 1,
        smooth: true,
        // label: {
        //   normal: {
        //     show: true,
        //     formatter: (params) => {
        //       let value = params?.data;
        //       return `${valueNullOrUndefinedReturnDash(
        //         value?.[`${selectedItem?.contentData}Loy`],
        //         selectedItem?.dataType,
        //       )}`;
        //     },
        //   },
        // },
        lineStyle: {
          normal: {
            opacity: 1,
            width: 3,
            color: color[1],
            type: 'dashed',
          },
        },
        itemStyle: {
          normal: {
            color: color[1],
          },
        },
        encode: {
          x: `${category}Name`,
          y: [`selectedDiffCnt`],
          tooltip: [`selectedDiffCnt`],
        },
      },
    ],
  };
  return option;
};

export { StatsSelectedTrendsLineOption };
