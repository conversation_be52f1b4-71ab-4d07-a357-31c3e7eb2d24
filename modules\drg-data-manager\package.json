{"name": "drg-hosp-decision-support", "private": true, "scripts": {"start": "umi dev", "build": "umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "dependencies": {"@ant-design/pro-components": "2.3.58", "react": "18.x", "react-dom": "18.x", "umi": "3.5.34", "ahooks": "3.7.1", "qs": "^6.11.0", "@uni/components": "workspace:^", "@uni/utils": "workspace:^", "@uni/services": "workspace:^", "@uni/commons": "workspace:^"}, "devDependencies": {"babel-loader": "^8.2.5", "babel-plugin-import": "^1.13.5", "typescript": "^4.1.2", "@types/react": "18.x", "@types/react-dom": "18.x", "@umijs/plugin-qiankun": "^2.42.0", "@umijs/preset-react": "1.x", "@umijs/test": "3.5.34", "yorkie": "^2.0.0"}}