import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import {
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { useModel } from '@@/plugin-model/useModel';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import IconBtn from '@uni/components/src/iconBtn';
import _ from 'lodash';
import {
  Button,
  Card,
  message,
  Modal,
  TableProps,
  Spin,
  Row,
  Col,
  Tabs,
  Space,
  Tooltip,
  Divider,
  Dropdown,
  MenuProps,
  Typography,
  Drawer,
} from 'antd';
import { ExportIconBtn, UniTable } from '@uni/components/src';
import dayjs from 'dayjs';
import { ProForm, ProFormInstance } from '@uni/components/src/pro-form';
import ProFormContainer from '@uni/components/src/pro-form-container';
import './index.less';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { isEmptyValues } from '@uni/utils/src/utils';

const canEditColumn =
  (window as any).externalConfig?.['his']?.devtool?.editColumn ?? false;

const ExportDetail = () => {
  const {
    globalState: { searchParams, dictData },
  } = useModel('@@qiankunStateFromMaster');

  let { dateRange, hospCodes, CliDepts, insurType } = searchParams;

  const [requestParams, setRequestParams] = useState<any>(undefined);

  // 主table
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any> & { fetchParams: any }, IReducer>
  >(tableReducer, { ...InitTableState, fetchParams: undefined });

  // table-part:columns
  const {
    loading: tableColumnsFetchLoading,
    mutate: tableColumnsMutate,
    run: tableColumnsFetch,
  } = useRequest(
    () =>
      uniCommonService('Api/FundSupervise/LatestDipSettleDetail/GetList', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      }),
    {
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          TableDispatch({
            type: TableAction.columnsChange,
            payload: {
              columns: tableColumnBaseProcessor([], res?.data?.Columns),
            },
          });
        }
        return null;
      },
    },
  );
  // table-part:datasource
  const {
    data: tablePart,
    loading: tablePartFetchLoading,
    run: tablePartFetch,
  } = useRequest(
    (data, pagi, sorter = null) =>
      uniCommonService('Api/FundSupervise/LatestDipSettleDetail/GetList', {
        method: 'POST',
        data: {
          DtParam: {
            Draw: 1,
            Start: (pagi.cur - 1) * pagi.size,
            Length: pagi.size,
          },
          ...data,
        },
      }),
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          TableDispatch({
            type: TableAction.dataPagiChange,
            payload: {
              data: data?.data ?? [],
              backPagination: {
                ...TableState.backPagination,
                current: params?.at(1)?.cur,
                pageSize: params?.at(1)?.size,
                total: data?.recordsFiltered ?? 0,
              },
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), TableState.sorter)) {
            TableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    tablePartFetch(requestParams, { cur: pagi.current, size: pagi.pageSize });
  };

  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      isEmptyValues(requestParams)
    ) {
      if (dateRange?.length) {
        let tableParams = {
          SettleSdate: dateRange?.at(0),
          SettleEdate: dateRange?.at(1),
          HospCode: hospCodes,
          CliDepts,
          insurType,
        };
        setRequestParams(tableParams);
      }
    }
  }, [searchParams]);

  useEffect(() => {
    if (requestParams) {
      tablePartFetch(requestParams, {
        cur: 1,
        size:
          TableState.backPagination?.pageSize ??
          TableState.backPagination?.defaultPageSize,
      });
    }
  }, [requestParams]);

  return (
    <Card
      title={'分组明细'}
      extra={
        <Space>
          <ExportIconBtn
            isBackend={true}
            backendObj={{
              url: `Api/FundSupervise/LatestDipSettleDetail/ExportGetList`,
              method: 'POST',
              data: requestParams,
              fileName: '分组明细',
            }}
            btnDisabled={TableState?.data?.length < 1}
          />
          {canEditColumn && (
            <TableColumnEditButton
              {...{
                columnInterfaceUrl:
                  'Api/FundSupervise/LatestDipSettleDetail/GetList',
                onTableRowSaveSuccess: (newColumns) => {
                  TableDispatch({
                    type: TableAction.columnsChange,
                    payload: {
                      columns: tableColumnBaseProcessor([], newColumns),
                    },
                  });
                  tableColumnsMutate(newColumns);
                },
              }}
            />
          )}
        </Space>
      }
    >
      <UniTable
        id={'dip-pay-detail-table'}
        rowKey={'HisId'}
        dictionaryData={dictData}
        scroll={{ x: 'max-content' }}
        loading={tablePartFetchLoading}
        columns={TableState.columns}
        dataSource={TableState.data}
        pagination={TableState.backPagination}
        onChange={backTableOnChange}
        toolBarRender={null}
        isBackPagination
      />
    </Card>
  );
};

export default ExportDetail;
