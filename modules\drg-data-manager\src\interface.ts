import { TableColumnsType } from 'antd';
import { SorterResult } from 'antd/lib/table/interface';

interface IColumnItem {
  data: string;
  title: string;
  name: string;
  visible: boolean;
  orderable: boolean;
  orderMode?: string;
  orderPriority?: number;
  aggregable?: false;
  responsivePriority?: number;
  className?: string;
  dataType?: string;
  scale?: string | number | boolean;
  dictionaryModule?: string;
  groupName?: string;
  shortTitle?: string;
  shortTitleDescription?: string;
  sorter: boolean;
  dataIndex: string;
  defaultRenderByType?: boolean;
}

// table data type
export interface ITableState<DataType> {
  columns?: (TableColumnsType & IColumnItem)[];
  data?: DataType[];
  total?: number;
  clkItem?: DataType;
}

//cmi stat data type
export interface IStatState<DataType> extends ITableState<DataType> {
  compareStatRes?: {
    Loms?: DataType[];
    Loys?: DataType[];
    Moms?: DataType[];
    Yoys?: DataType[];
  };
}

// dyn-ddr 接口的List (table datas)
export interface SwagDynddrListItems<ItemType> {
  Items?: ItemType[];
  TotalCount: number;
  MaxResultCount: number;
  pageCount: number;
}

// useReducer里面的reducer
export type IReducer<T = any> = {
  type: string;
  payload?: T;
};

// table rowSelection
export interface IRowSelection<T> {
  selectedKeys?: string[] | React.Key[];
  selectedRecords?: T[];
}

// table sorter
export interface ISorter<DataType> {
  sorter?: SorterResult<DataType>;
}

// table req
export interface ITableReq {
  columns: (TableColumnsType & IColumnItem)[];
  datas: { data: any[]; total: number }[];
}

// modal
export type IModalState<T> = {
  visible?: boolean;
  record?: T;
  actionType?: string;
};

// 病案时间轴
export interface SwagMrTimelineItem {
  ActionId: number;
  RecordId: number;
  OpDate: string;
  Operator: string;
  ActionType: string;
}
