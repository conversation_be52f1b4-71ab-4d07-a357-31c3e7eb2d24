import { Select, Col, Row, Space, Divider } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import UniEcharts from '@uni/components/src/echarts';
import { BubbleGradientOption } from './echarts.opts';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { useDeepCompareEffect } from 'ahooks';

const CustomGradientChart = (props) => {
  // gradient opts
  const [gradientOpts, setGradientOpts] = useState({});

  // opts
  useDeepCompareEffect(() => {
    if (props?.dataSource.length > 0 && props?.quadrantSelectValue) {
      console.log(
        'BubbleGradientOption',
        props?.quadrantSelectValue,
        BubbleGradientOption(
          props?.dataSource,
          props?.quadrantSelectValue,
          props?.category,
        ),
      );
      setGradientOpts(
        BubbleGradientOption(
          props?.dataSource,
          props?.quadrantSelectValue,
          props?.category,
        ),
      );
    }
  }, [props?.category, props?.dataSource, props?.quadrantSelectValue]);

  return (
    <>
      <UniEcharts
        elementId={props?.id || `bubble_gradient`}
        height={props?.height || 350}
        loading={props?.loading || false}
        options={gradientOpts}
        mouseEvents={props?.mouseEvents}
      />
    </>
  );
};

export default CustomGradientChart;
